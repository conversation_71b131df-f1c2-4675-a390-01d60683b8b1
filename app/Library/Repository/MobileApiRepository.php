<?php

 namespace App\Library\Repository;
 use App\Library\Helper\AcademicHelper;
 use Illuminate\Support\Facades\DB;
 use Illuminate\Database\Eloquent\ModelNotFoundException;
 use App\Models\User;
 use App\Models\MobileDevice;
 use App\Models\StudentStorage;
 use App\Models\StudentStorageGlobal;
 use App\Models\SchoolBusData;
 use App\Models\GpsTrack;
 use App\Models\StudentPickupRequest;
 use Illuminate\Support\Str;
 use App\Models\Branch;
 use App\Library\Helper\GeneralHelper;
 use App\Models\Assessment;
 use App\Models\FormativeAssessmentData;
 use App\Models\SummativeAssessmentData;
 use Carbon\Carbon;
 use App\Models\StudentInformation;
 use App\Models\ElectiveGradeStudent;
 use App\Models\Timetable;
 use App\Models\StudentClassAttendance;
 use App\Models\DisciplineRecord;
 use App\Models\DisciplineItem;
 use App\Models\DisciplineAwardRemainingPoint;
 use App\Models\DetentionRecord;
 use App\Models\HomeworkDetail;
 use App\Models\AcademicYear;
 use App\Models\MobileAboutInfo;
 use App\Models\MobileFAQ;
 use App\Models\HealthStudent;
 use App\Models\HealthStaff;
 use App\Models\HealthGuest;
 use App\Models\StudentHealthInformation;
 use App\Models\StudentMeasurements;
 use App\Models\Dictionary;
 use App\Models\BranchGoogleCalendar;
 use App\Models\CalendarGlobal;
 use App\Models\CalendarPersonal;
 use App\Models\AcademicCalendar;
 use App\Library\Repository\MobileNotificationRepository;
 use App\Library\Repository\NotificationRepository;
 use App\Library\Repository\GoogleDriveRepository;
 use App\Library\Services\GoogleDriveService;
 use App\Library\Services\GoogleCalendarService;

 class MobileApiRepository {
    protected $ah;
    protected $mobileNotification;
    protected $notification;
    protected $driveRepository;

    public function __construct() {
        $this->ah = new AcademicHelper();
        $this->mobileNotification = new MobileNotificationRepository($this->ah);
        $this->notification = new NotificationRepository($this->ah);

        // Initialize Google Drive repository with error handling
        try {
            $driveService = new GoogleDriveService();
            $this->driveRepository = new GoogleDriveRepository($driveService, $this->ah);
        } catch (\Exception $e) {
            \Log::warning('Failed to initialize Google Drive service: ' . $e->getMessage());
            $this->driveRepository = null;
        }
    }

    public function studentFiles($authCode) {
        $fileData = null;
        $device = MobileDevice::where('auth_code', $authCode)->first();
        if($device) {
            $userInfo = User::find($device->student_id);
            $globalFiles = StudentStorageGlobal::where('branch_id', $userInfo->branch_id)->get();
            $studentFiles = StudentStorage::where('student_id', $device->student_id)->get();
            $counter = 1;
            foreach ($studentFiles as $key => $fl) {
                $fileData .= $fl->file_name."|*|https://sis.bfi.edu.mm/".$fl->file_url."/*/";
            }
                  foreach ($globalFiles as $key => $fl) {
                $fileData .= $fl->file_name."|*|https://sis.bfi.edu.mm/".$fl->file_url."/*/";
            }
        }
        return $fileData;
    }

    public function checkDeviceData($data) {
        // Method implementation removed - no longer needed
    }

    /**
     * Check authentication code and return user information
     */
    public function checkAuthCode($authCode) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return [
                    'success' => false,
                    'message' => 'Invalid authentication code'
                ];
            }

            // Update last_login timestamp
            $device->update([
                'last_login' => now(),
                'updated_at' => now()
            ]);

            return [
                'success' => true,
                'user_id' => $device->student_id,
                'user_type' => $device->user_type,
                'device' => $device
            ];

        } catch (\Exception $e) {
            \Log::error('Authentication check failed', [
                'auth_code' => $authCode,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Authentication failed'
            ];
        }
    }

    public function removeUserFromDevice($data) {
        return MobileDevice::where('device_token', $data['deviceToken'])
                    ->where('student_id', $data['userId'])
                    ->delete();
    }

    /**
     * Update last_login timestamp when user opens the app
     */
    public function updateLastLogin($authCode) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            // Update last_login timestamp
            $device->update([
                'last_login' => now(),
                'updated_at' => now()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Last login updated successfully',
                'data' => [
                    'user_id' => $device->student_id,
                    'user_type' => $device->user_type,
                    'last_login' => $device->last_login,
                    'updated_at' => now()->toISOString()
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Update last login error', [
                'auth_code' => $authCode,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['error' => 'Failed to update last login'], 500);
        }
    }

    /**
     * Update device activity with optional additional information
     */
    public function updateDeviceActivity($authCode, $additionalData = []) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            // Prepare update data
            $updateData = [
                'last_login' => now(),
                'updated_at' => now()
            ];

            // Add optional additional data if provided
            if (isset($additionalData['app_version'])) {
                $updateData['app_version'] = $additionalData['app_version'];
            }
            if (isset($additionalData['device_info'])) {
                $updateData['device_info'] = $additionalData['device_info'];
            }

            // Update device record
            $device->update($updateData);

            return response()->json([
                'success' => true,
                'message' => 'Device activity updated successfully',
                'data' => [
                    'user_id' => $device->student_id,
                    'user_type' => $device->user_type,
                    'device_type' => $device->device_type,
                    'last_login' => $device->fresh()->last_login,
                    'updated_fields' => array_keys($updateData),
                    'updated_at' => now()->toISOString()
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Update device activity error', [
                'auth_code' => $authCode,
                'additional_data' => $additionalData,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['error' => 'Failed to update device activity'], 500);
        }
    }

    /**
     * Clean up mobile devices with last_login older than specified days
     */
    public function cleanupOldDevices($daysOld = 30) {
        try {
            $cutoffDate = now()->subDays($daysOld);

            // Get devices that will be deleted for logging
            $devicesToDelete = MobileDevice::where('last_login', '<', $cutoffDate)
                ->orWhereNull('last_login')
                ->get();

            if ($devicesToDelete->isEmpty()) {
                return response()->json([
                    'success' => true,
                    'message' => 'No old devices found to cleanup',
                    'data' => [
                        'cutoff_date' => $cutoffDate->toISOString(),
                        'days_old' => $daysOld,
                        'devices_removed' => 0,
                        'cleaned_at' => now()->toISOString()
                    ]
                ]);
            }

            // Log devices that will be removed
            \Log::info('Cleaning up old mobile devices', [
                'cutoff_date' => $cutoffDate->toISOString(),
                'days_old' => $daysOld,
                'devices_count' => $devicesToDelete->count(),
                'devices' => $devicesToDelete->map(function($device) {
                    return [
                        'device_id' => $device->mobile_device_id,
                        'user_id' => $device->student_id,
                        'user_type' => $device->user_type,
                        'device_type' => $device->device_type,
                        'last_login' => $device->last_login,
                        'created_at' => $device->created_at
                    ];
                })->toArray()
            ]);

            // Delete old devices
            $deletedCount = MobileDevice::where('last_login', '<', $cutoffDate)
                ->orWhereNull('last_login')
                ->delete();

            \Log::info('Mobile devices cleanup completed', [
                'deleted_count' => $deletedCount,
                'cutoff_date' => $cutoffDate->toISOString()
            ]);

            return response()->json([
                'success' => true,
                'message' => "Successfully removed {$deletedCount} old mobile devices",
                'data' => [
                    'cutoff_date' => $cutoffDate->toISOString(),
                    'days_old' => $daysOld,
                    'devices_removed' => $deletedCount,
                    'cleaned_at' => now()->toISOString()
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Mobile devices cleanup error', [
                'days_old' => $daysOld,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['error' => 'Failed to cleanup old devices'], 500);
        }
    }

    /**
     * Get statistics about mobile devices by age
     */
    public function getDeviceStatistics() {
        try {
            $now = now();
            $oneWeekAgo = $now->copy()->subWeek();
            $oneMonthAgo = $now->copy()->subMonth();
            $threeMonthsAgo = $now->copy()->subMonths(3);

            $stats = [
                'total_devices' => MobileDevice::count(),
                'active_last_week' => MobileDevice::where('last_login', '>=', $oneWeekAgo)->count(),
                'active_last_month' => MobileDevice::where('last_login', '>=', $oneMonthAgo)->count(),
                'older_than_month' => MobileDevice::where('last_login', '<', $oneMonthAgo)
                    ->orWhereNull('last_login')->count(),
                'older_than_3_months' => MobileDevice::where('last_login', '<', $threeMonthsAgo)
                    ->orWhereNull('last_login')->count(),
                'never_logged_in' => MobileDevice::whereNull('last_login')->count(),
                'by_user_type' => MobileDevice::selectRaw('user_type, COUNT(*) as count')
                    ->groupBy('user_type')->get()->pluck('count', 'user_type'),
                'by_device_type' => MobileDevice::selectRaw('device_type, COUNT(*) as count')
                    ->groupBy('device_type')->get()->pluck('count', 'device_type')
            ];

            return response()->json([
                'success' => true,
                'message' => 'Device statistics retrieved successfully',
                'data' => $stats,
                'generated_at' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            \Log::error('Device statistics error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['error' => 'Failed to get device statistics'], 500);
        }
    }

    /**
     * Logout user from mobile device using authCode
     */
    public function logoutUser($authCode) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            $userId = $device->student_id;
            $userType = $device->user_type;
            $deviceToken = $device->device_token;

            // Log the logout action
            \Log::info('Mobile user logout', [
                'user_id' => $userId,
                'user_type' => $userType,
                'device_token' => substr($deviceToken, 0, 20) . '...',
                'device_type' => $device->device_type,
                'ip' => request()->ip()
            ]);

            // Delete the device record (logout)
            $deleted = $device->delete();

            if ($deleted) {
                return response()->json([
                    'success' => true,
                    'message' => 'Successfully logged out',
                    'data' => [
                        'user_id' => $userId,
                        'user_type' => $userType,
                        'logged_out_at' => now()->toISOString()
                    ]
                ]);
            } else {
                return response()->json(['error' => 'Failed to logout'], 500);
            }

        } catch (\Exception $e) {
            \Log::error('Mobile logout error', [
                'auth_code' => $authCode,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['error' => 'Logout failed'], 500);
        }
    }

    /**
     * Logout user from all devices (security function)
     */
    public function logoutUserFromAllDevices($userId) {
        try {
            $devices = MobileDevice::where('student_id', $userId)->get();

            if ($devices->isEmpty()) {
                return response()->json([
                    'success' => true,
                    'message' => 'No devices found for user',
                    'data' => [
                        'user_id' => $userId,
                        'devices_removed' => 0
                    ]
                ]);
            }

            // Log the action
            \Log::info('Mobile user logout from all devices', [
                'user_id' => $userId,
                'device_count' => $devices->count(),
                'ip' => request()->ip()
            ]);

            // Delete all device records for the user
            $deletedCount = MobileDevice::where('student_id', $userId)->delete();

            return response()->json([
                'success' => true,
                'message' => 'Successfully logged out from all devices',
                'data' => [
                    'user_id' => $userId,
                    'devices_removed' => $deletedCount,
                    'logged_out_at' => now()->toISOString()
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Mobile logout all devices error', [
                'user_id' => $userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['error' => 'Logout from all devices failed'], 500);
        }
    }

    public function getStudentTracking($authCode) {
        $rd = null;
        $device = MobileDevice::where('auth_code', $authCode)->first();
        if($device) {
            $trackData = SchoolBusData::where('student_id', $device->student_id)->first();
            if($trackData) {
                $lon = $trackData->home_longtitude;
                $lat = $trackData->home_latitude;

                $busTrackData = GpsTrack::where('id_device', $trackData->schoolbus_id)
                                        ->orderBy('id_track', 'desc')
                                        ->first();

                if($busTrackData) {
                    $rd = 'ok|'.$lat.'|'.$lon.'|'.$busTrackData->latitude.'|'.$busTrackData->longitude;
                } else {
                    $rd = 'fail|';
                }
            } else {
                $rd = 'fail|';
            }
        } else {
            $rd = 'fail|';
        }
        return $rd;
    }

    public function mobileNotifications($userIDS) {
        $arr = explode("|", $userIDS);

        $notifications = \DB::table('api_notifications')
                        ->select('notification_body', 'created_at', 'notification_title')
                        ->whereIn('user_id', $arr)
                        ->groupBy('notification_body', 'created_at', 'notification_title')
                        ->orderBy('created_at', 'desc')
                        ->take(50)
                        ->get();

        $myObj = new \stdClass;
        $jsonString = null;
        $counter = 0;
        foreach ($notifications as $key => $notification) {
            $notificationTimeArr = explode(" ", $notification->created_at);
            $myObj->notificationTitle = $notification->notification_title;
            $myObj->notificationBody = $notification->notification_body;
            $myObj->notificationDate = $notificationTimeArr[0];
            $myObj->notificationTime = $notificationTimeArr[1];
            $myObj->notificationType = null;
            $myJson = json_encode($myObj);
            $jsonString = $jsonString.$myJson;
            $counter++;
        }

        if($counter == 0) {
            $myObj->notificationTitle = "No new notifications";
            $myObj->notificationBody = "We could not find any new notifications.";
            $myObj->notificationDate = " ";
            $myObj->notificationTime = " ";
            $myObj->notificationType = "all";
            $myJson = json_encode($myObj);
            $jsonString = $jsonString.$myJson;
        }

        $jsonString = str_replace("}{","},{",$jsonString);
        $jsonString = "[".$jsonString."]";
        return $jsonString;
    }

    public function createPickupRequest($data) {
        $rd = '';

        $carbonDate = Carbon::now();

        $gh = new GeneralHelper();

        $device = MobileDevice::where('auth_code', $data['authCode'])->first();
        if($device) {

          $userInfo = User::find($device->student_id);
          $branchInfo = Branch::Find($userInfo->branch_id);

          $branchLocation = $branchInfo->gps_location;
          $branchLocArr = explode(",", $branchLocation);

          $distance = $gh->getGpsDistance($branchLocArr[0], $branchLocArr[1], $data['lat'], $data['lon']);

          if($distance <= 150) {

            StudentPickupRequest::firstOrCreate(
              [
                'request_date'    => $carbonDate->format('Y-m-d'),
                'student_id'      => $device->student_id,
                'branch_id'       => $device->student->branch_id,
              ],
              [
                'request_status'  => 0,
                'uuid'              => Str::uuid(),
                'parent_distance'   => $distance." m"
              ]
          );
            $rd = 'ok| Your request recorded for student: ['.$device->student->name.']';
          } else {
            $rd = 'fail|You are too far from campus. Please make request when you are at least 150 meters close to campus! ['.$distance.'m]';
          }
        } else {
            $rd = 'fail|Invalid Auth Code!';
        }
        return $rd;
    }

    public function getStudentTimetable($authCode) {
        $ah = new AcademicHelper();
        $device = MobileDevice::where('auth_code', $authCode)->first();

        if (!$device) {
            return response()->json(['error' => 'Invalid authentication code'], 401);
        }

        $branch = StudentInformation::where("id", $device->student_id)->first();
        $courses = ElectiveGradeStudent::select('grade_id')
            ->distinct('grade_id')
            ->where('student_id', $device->student_id)
            ->get();

        $grades = [];
        foreach ($courses as $course) {
            $grades[] = $course->grade_id;
        }

        $timetable = Timetable::with(['user:id,name', 'subject:subject_id,subject_name'])
            ->where('academic_year_id', $ah->branchAcademicYear($branch->branch_id))
            ->where('branch_id', $device->student->branch_id)
            ->whereIn('grade_id', $grades)
            ->get()
            ->groupBy('week_day');

        return response()->json($timetable);
    }

    public function getStudentAttendanceData($authCode) {
        $device = MobileDevice::where('auth_code', $authCode)->first();

        if (!$device) {
            return response()->json(['error' => 'Invalid authentication code'], 401);
        }

        // Get student branch information
        $branch = StudentInformation::where("id", $device->student_id)->first();

        if (!$branch) {
            return response()->json(['error' => 'Student information not found'], 404);
        }

        // Get current academic year for the branch
        $academicYearId = $this->ah->branchAcademicYear($branch->branch_id);

        if (!$academicYearId) {
            return response()->json(['error' => 'Academic year not found for this branch'], 404);
        }

        // Get academic year start date
        $academicYear = AcademicYear::where('academic_year_id', $academicYearId)->first();

        if (!$academicYear) {
            return response()->json(['error' => 'Academic year details not found'], 404);
        }

        // Get attendance data from academic year start date to current date
        $startDate = $academicYear->start_date;
        $currentDate = date('Y-m-d');

        // Get all attendance records for the student
        $attendanceData = StudentClassAttendance::leftJoin('academic_elective_grade', 'academic_elective_grade.grade_id', 'students_attendance_class.grade_id')
            ->leftJoin('subjects', 'subjects.subject_id', 'students_attendance_class.subject_id')
            ->where('students_attendance_class.date', '>=', $startDate)
            ->where('students_attendance_class.date', '<=', $currentDate)
            ->where('students_attendance_class.student_id', $device->student_id)
            ->where('students_attendance_class.academic_year_id', $academicYearId)
            ->orderBy('students_attendance_class.date', 'desc')
            ->orderBy('students_attendance_class.week_time', 'asc')
            ->select(
                'students_attendance_class.*',
                'academic_elective_grade.grade_name',
                'subjects.subject_name'
            )
            ->get();

        // Format attendance records
        $formattedData = [];
        foreach ($attendanceData as $item) {
            $timestamp = strtotime($item->date);
            $weekday = date("l", $timestamp);

            $formattedData[] = [
                'date' => $item->date,
                'weekday' => $weekday,
                'subject' => $item->subject_name ?? $item->grade_name,
                'grade' => $item->grade_name,
                'period' => $item->week_time,
                'status' => strtoupper($item->attendance_status),
                'attendance_note' => $item->attendance_note ?? null
            ];
        }

        // Generate daily statistics from start_date to current date
        $dailyStats = [];
        $totalDays = 0;
        $daysWithAttendance = 0;
        $totalPresentCount = 0;
        $totalLateCount = 0;
        $totalAbsentCount = 0;
        $totalAttendanceRecords = 0;

        // Create date range from start_date to current_date
        $startDateTime = new \DateTime($startDate);
        $currentDateTime = new \DateTime($currentDate);
        $interval = new \DateInterval('P1D');
        $dateRange = new \DatePeriod($startDateTime, $interval, $currentDateTime->modify('+1 day'));

        foreach ($dateRange as $date) {
            $dateString = $date->format('Y-m-d');
            $weekday = $date->format('l');

            // Skip weekends (Saturday and Sunday) for school attendance
            if (in_array($weekday, ['Saturday', 'Sunday'])) {
                continue;
            }

            $totalDays++;

            // Get attendance records for this specific date
            $dayAttendance = $attendanceData->where('date', $dateString);
            $dayPresentCount = $dayAttendance->where('attendance_status', 'present')->count();
            $dayLateCount = $dayAttendance->where('attendance_status', 'late')->count();
            $dayAbsentCount = $dayAttendance->where('attendance_status', 'absent')->count();
            $dayTotalRecords = $dayAttendance->count();

            if ($dayTotalRecords > 0) {
                $daysWithAttendance++;
            }

            $totalPresentCount += $dayPresentCount;
            $totalLateCount += $dayLateCount;
            $totalAbsentCount += $dayAbsentCount;
            $totalAttendanceRecords += $dayTotalRecords;

            $dailyStats[] = [
                'date' => $dateString,
                'weekday' => $weekday,
                'present_count' => $dayPresentCount,
                'late_count' => $dayLateCount,
                'absent_count' => $dayAbsentCount,
                'total_periods' => $dayTotalRecords,
                'has_attendance' => $dayTotalRecords > 0,
                'attendance_percentage' => $dayTotalRecords > 0 ? round((($dayPresentCount + $dayLateCount) / $dayTotalRecords) * 100, 1) : 0
            ];
        }

        // Calculate overall statistics
        $overallAttendancePercentage = $totalAttendanceRecords > 0 ?
            round((($totalPresentCount + $totalLateCount) / $totalAttendanceRecords) * 100, 1) : 0;

        $attendanceRate = $totalDays > 0 ? round(($daysWithAttendance / $totalDays) * 100, 1) : 0;

        return response()->json([
            'success' => true,
            'attendance_records' => $formattedData,
            'daily_statistics' => array_reverse($dailyStats), // Most recent first
            'summary_statistics' => [
                'total_school_days' => $totalDays,
                'days_with_attendance' => $daysWithAttendance,
                'days_without_attendance' => $totalDays - $daysWithAttendance,
                'attendance_rate' => $attendanceRate, // Percentage of days with any attendance record
                'total_attendance_records' => $totalAttendanceRecords,
                'total_present' => $totalPresentCount,
                'total_late' => $totalLateCount,
                'total_absent' => $totalAbsentCount,
                'overall_attendance_percentage' => $overallAttendancePercentage,
                'academic_year_start' => $startDate,
                'current_date' => $currentDate,
                'date_range_days' => $totalDays
            ]
        ]);
    }

     public function getStudentBpsData($authCode) {
             $device = MobileDevice::where('auth_code', $authCode)->first();

             if (!$device) {
                 return response()->json(['error' => 'Invalid authentication code'], 401);
             }

             $branch = StudentInformation::where("id", $device->student_id)->first();

             if (!$branch) {
                 return response()->json(['error' => 'Student information not found'], 404);
             }

             $records = DisciplineRecord::leftJoin('discipline_bps_items', 'discipline_bps_items.discipline_item_id', 'item_id')
                 ->leftJoin('users', 'users.id', 'discipline_bps_records.user_id')
                 ->where('student_id', $device->student_id)
                 ->where('academic_year_id', $this->ah->branchAcademicYear($branch->branch_id))
                 ->where('discipline_bps_records.status', 1)
                 ->orderBy('date', 'desc')
                 ->select(
                     'discipline_bps_records.*',
                     'discipline_bps_items.item_title',
                     'discipline_bps_items.item_point',
                     'users.name as teacher_name'
                 )
                 ->get();

             $formattedData = [];
             foreach ($records as $record) {
                 $formattedData[] = [
                     'id' => $record->discipline_record_id,
                     'item_title' => $record->item_title,
                     'item_type' => strtoupper($record->item_type),
                     'item_point' => $record->item_point,
                     'date' => $record->date,
                     'note' => $record->note ?? '',
                     'teacher_name' => $record->teacher_name ?? 'N/A',
                     'status' => $record->status
                 ];
             }

             // Get detention records for the student
             $detentionRecords = \App\Models\DetentionRecord::leftJoin('discipline_bps_records', 'detention_records.dps_record_id', 'discipline_bps_records.discipline_record_id')
                 ->leftJoin('discipline_bps_items', 'discipline_bps_items.discipline_item_id', 'discipline_bps_records.item_id')
                 ->leftJoin('users', 'users.id', 'discipline_bps_records.user_id')
                 ->where('detention_records.student_id', $device->student_id)
                 ->where('detention_records.academic_year_id', $this->ah->branchAcademicYear($branch->branch_id))
                 ->orderBy('detention_records.date', 'desc')
                 ->select(
                     'detention_records.*',
                     'discipline_bps_items.item_title',
                     'discipline_bps_items.item_point',
                     'users.name as teacher_name'
                 )
                 ->get();

             $formattedDetentionData = [];
             foreach ($detentionRecords as $detention) {
                 $formattedDetentionData[] = [
                     'id' => $detention->detention_record_id,
                     'type' => 'DETENTION',
                     'detention_type' => $detention->detention_type,
                     'served_detention_type' => $detention->served_detention_type,
                     'is_served' => $detention->is_served,
                     'system_note' => $detention->system_note,
                     'item_title' => $detention->item_title ?? 'Detention Record',
                     'item_point' => $detention->item_point ?? 0,
                     'latest_point' => $detention->latest_point,
                     'date' => $detention->date,
                     'teacher_name' => $detention->teacher_name ?? 'System',
                     'academic_semester' => $detention->academic_semester
                 ];
             }

             // Calculate total points
             $totalPoints = $records->sum('item_point');

             return response()->json([
                 'success' => true,
                 'bps_records' => $formattedData,
                 'detention_records' => $formattedDetentionData,
                 'total_bps_records' => count($formattedData),
                 'total_detention_records' => count($formattedDetentionData),
                 'total_points' => $totalPoints
             ]);
         }

     public function getStudentHomeworkData($authCode) {
             $ah = new AcademicHelper();
             $device = MobileDevice::where('auth_code', $authCode)->first();

             if (!$device) {
                 return response()->json(['error' => 'Invalid authentication code'], 401);
             }

             $branch = StudentInformation::where("id", $device->student_id)->first();

             if (!$branch) {
                 return response()->json(['error' => 'Student information not found'], 404);
             }

         // Get homework data with all required fields from both tables
         $homeworks = HomeworkDetail::select([
                 // All fields from academic_homework_detail
                 'academic_homework_detail.*',
                 // Required fields from academic_homework
                 'academic_homework.title',
                 'academic_homework.homework_data',
                 'academic_homework.homework_files',
                 'academic_homework.homework_video_links',
                 'academic_homework.deadline',
                 'academic_homework.user_id as teacher_id',
                 'academic_homework.grade_id',
                 'academic_homework.created_at as homework_created_at',
                 // Google Drive integration fields
                 'academic_homework.google_drive_folder_id',
                 'academic_homework.google_drive_files',
                 // Get subject and teacher info through joins
                 'users.name as teacher_name',
                 'academic_elective_grade.grade_name',
                 \DB::raw('COALESCE(subjects.subject_name, "Unknown Subject") as subject_name'),
                 // Get folder information
                 'homework_folders.folder_name',
                 'homework_folders.description as folder_description'
             ])
             ->leftJoin('academic_homework', 'academic_homework.homework_id', 'academic_homework_detail.homework_id')
             ->leftJoin('users', 'users.id', 'academic_homework.user_id')
             ->leftJoin('academic_elective_grade', 'academic_elective_grade.grade_id', 'academic_homework.grade_id')
             ->leftJoin('subjects', 'subjects.subject_id', 'academic_elective_grade.subject_id')
             ->leftJoin('homework_folders', 'homework_folders.google_drive_folder_id', 'academic_homework.google_drive_folder_id')
             ->where('academic_homework.academic_year_id', $ah->branchAcademicYear($branch->branch_id))
             ->where('academic_homework.branch_id', $branch->branch_id)
             ->where('academic_homework.homework_status', 1)
             ->where('academic_homework_detail.student_id', $device->student_id)
             ->orderBy('academic_homework.homework_id', 'desc')
             ->get();

             $formattedData = [];
             foreach ($homeworks as $homework) {

                 // Parse Google Drive files if available
                 $googleDriveFiles = null;
                 if (!empty($homework->google_drive_files)) {
                     $googleDriveFiles = json_decode($homework->google_drive_files, true);
                 }

                 $formattedData[] = [
                     // Homework detail fields (from academic_homework_detail)
                     'detail_id' => $homework->detail_id,
                     'homework_id' => $homework->homework_id,
                     'student_id' => $homework->student_id,
                     'uuid' => $homework->uuid,
                     'is_completed' => $homework->is_completed,
                     'reply_file' => $homework->reply_file,
                     'reply_data' => $homework->reply_data,
                     'teacher_comment' => $homework->teacher_comment,
                     'approval_status' => $homework->approval_status ?? null,
                     'reviewed_at' => $homework->reviewed_at ?? null,
                     'viewed_at' => $homework->viewed_at,
                     'submitted_date' => $homework->sumitted_date, // Note: keeping original field name for compatibility
                     'created_at' => $homework->created_at,
                     'updated_at' => $homework->updated_at,

                     // Homework main fields (from academic_homework)
                     'title' => $homework->title,
                     'homework_data' => $homework->homework_data,
                     'homework_files' => $homework->homework_files,
                     'homework_video_links' => $homework->homework_video_links,
                     'deadline' => $homework->deadline,
                     'homework_created_at' => $homework->homework_created_at,

                     // Google Drive integration fields
                     'google_drive_folder_id' => $homework->google_drive_folder_id,
                     'google_drive_files' => $googleDriveFiles,
                     'folder_info' => [
                         'folder_name' => $homework->folder_name,
                         'folder_description' => $homework->folder_description,
                         'has_folder' => !empty($homework->google_drive_folder_id)
                     ],

                     // Related information (from joins)
                     'teacher_name' => $homework->teacher_name ?? 'N/A',
                     'subject_name' => $homework->subject_name ?? 'Unknown Subject',
                     'grade_name' => $homework->grade_name ?? 'N/A',

                     // Status indicators
                     'is_overdue' => $homework->deadline && strtotime($homework->deadline) < time() && !$homework->is_completed,
                     'has_teacher_files' => !empty($homework->homework_files) || !empty($googleDriveFiles),
                     'has_teacher_videos' => !empty($homework->homework_video_links),
                     'has_student_submission' => !empty($homework->reply_file) || !empty($homework->reply_data),
                     'is_viewed' => !empty($homework->viewed_at),
                     'has_google_drive_integration' => !empty($homework->google_drive_folder_id),
                     'is_approved' => isset($homework->approval_status) && $homework->approval_status === 'approved',
                     'is_rejected' => isset($homework->approval_status) && $homework->approval_status === 'rejected',
                     'can_be_updated' => $this->canHomeworkBeUpdated($homework)
                 ];
             }

             // Calculate completion statistics
             $totalHomeworks = count($formattedData);
             $completedHomeworks = collect($formattedData)->where('is_completed', 1)->count();
             $pendingHomeworks = $totalHomeworks - $completedHomeworks;
             $viewedHomeworks = collect($formattedData)->where('is_viewed', true)->count();
             $overdueHomeworks = collect($formattedData)->where('is_overdue', true)->count();

             return response()->json([
                 'success' => true,
                 'data' => $formattedData,
                 'statistics' => [
                     'total_records' => $totalHomeworks,
                     'completed_count' => $completedHomeworks,
                     'pending_count' => $pendingHomeworks,
                     'viewed_count' => $viewedHomeworks,
                     'overdue_count' => $overdueHomeworks,
                     'completion_percentage' => $totalHomeworks > 0 ? round(($completedHomeworks / $totalHomeworks) * 100, 1) : 0,
                     'viewed_percentage' => $totalHomeworks > 0 ? round(($viewedHomeworks / $totalHomeworks) * 100, 1) : 0
                 ]
             ]);
         }

         /**
          * Mark homework as viewed when student opens it
          */
         public function markHomeworkAsViewed($authCode, $detailId) {
             $device = MobileDevice::where('auth_code', $authCode)->first();

             if (!$device) {
                 return response()->json(['error' => 'Invalid authentication code'], 401);
             }

             // Find the homework detail record for this student
             $homeworkDetail = HomeworkDetail::where('detail_id', $detailId)
                 ->where('student_id', $device->student_id)
                 ->first();

             if (!$homeworkDetail) {
                 return response()->json(['error' => 'Homework not found or access denied'], 404);
             }

             // Update viewed_at timestamp if not already set
             if (!$homeworkDetail->viewed_at) {
                 $homeworkDetail->viewed_at = now();
                 $homeworkDetail->save();
             }

             return response()->json([
                 'success' => true,
                 'message' => 'Homework marked as viewed',
                 'data' => [
                     'detail_id' => $homeworkDetail->detail_id,
                     'viewed_at' => $homeworkDetail->viewed_at,
                     'is_viewed' => true
                 ]
             ]);
         }

         /**
          * Submit homework by student
          */
         public function submitHomework($authCode, $data) {
             $device = MobileDevice::where('auth_code', $authCode)->first();

             if (!$device) {
                 return response()->json(['error' => 'Invalid authentication code'], 401);
             }

             // Validate required fields
             if (!isset($data['detail_id'])) {
                 return response()->json(['error' => 'Homework detail ID is required'], 400);
             }

             // Find the homework detail record for this student
             $homeworkDetail = HomeworkDetail::where('detail_id', $data['detail_id'])
                 ->where('student_id', $device->student_id)
                 ->first();

             if (!$homeworkDetail) {
                 return response()->json(['error' => 'Homework not found or access denied'], 404);
             }

             // Check if homework is already submitted
             if ($homeworkDetail->is_completed) {
                 return response()->json(['error' => 'Homework has already been submitted'], 400);
             }

             try {
                 \DB::beginTransaction();

                 // Update homework detail with submission data
                 $homeworkDetail->reply_file = $data['reply_file'] ?? $homeworkDetail->reply_file;
                 $homeworkDetail->reply_data = $data['reply_data'] ?? $homeworkDetail->reply_data;

                 // Mark as completed if there's any submission content
                 if (!empty($data['reply_file']) || !empty($data['reply_data'])) {
                     $homeworkDetail->is_completed = 1;
                 }

                 // Set submission date if not already set
                 if (!$homeworkDetail->sumitted_date) {
                     $homeworkDetail->sumitted_date = now();
                 }

                 // Mark as viewed if not already viewed
                 if (!$homeworkDetail->viewed_at) {
                     $homeworkDetail->viewed_at = now();
                 }

                 $homeworkDetail->save();

                 // Get homework and teacher information for notification
                 $homework = \App\Models\Homework::with(['grade.subject'])->find($homeworkDetail->homework_id);
                 $teacher = User::find($homework->user_id);
                 $student = User::find($homeworkDetail->student_id);

                 // Send immediate notification to teacher
                 $notificationSent = false;
                 $notificationMessage = '';
                 try {
                     // Get subject information for better notification context
                     $subject = $homework->grade->subject ?? null;
                     $subjectName = $subject ? $subject->subject_name : 'Unknown Subject';

                     $notificationData = [
                         'student' => $homework->user_id, // Send to teacher
                         'type' => 'homework_submitted',
                         'title' => 'Homework Submitted',
                         'message' => "Student {$student->name} has submitted homework: {$homework->title} ({$subjectName})",
                         'user_type' => 'staff',
                         'priority' => 'normal',
                         'category' => 'homework',
                         'data' => [
                             'homework_id' => $homework->homework_id,
                             'homework_title' => $homework->title,
                             'subject_name' => $subjectName,
                             'subject_id' => $subject ? $subject->subject_id : null,
                             'student_name' => $student->name,
                             'student_id' => $student->id,
                             'submission_type' => 'homework_submitted'
                         ]
                     ];

                     $this->mobileNotification->sendRealTime($notificationData);
                     $notificationSent = true;
                     $notificationMessage = 'Teacher notification sent successfully';
                 } catch (\Exception $e) {
                     // Don't fail homework submission if notification fails
                     $notificationSent = false;
                     $notificationMessage = 'Notification error: ' . $e->getMessage();
                 }

                 \DB::commit();

                 return response()->json([
                     'success' => true,
                     'message' => 'Homework submitted successfully',
                     'data' => [
                         'detail_id' => $homeworkDetail->detail_id,
                         'is_completed' => $homeworkDetail->is_completed,
                         'submitted_date' => $homeworkDetail->sumitted_date,
                         'viewed_at' => $homeworkDetail->viewed_at,
                         'reply_file' => $homeworkDetail->reply_file,
                         'reply_data' => $homeworkDetail->reply_data
                     ],
                     'notification' => [
                         'sent' => $notificationSent,
                         'message' => $notificationMessage
                     ]
                 ]);

             } catch (\Exception $e) {
                 \DB::rollBack();
                 return response()->json(['error' => 'Failed to submit homework'], 500);
             }
         }

         /**
          * Mark homework as done (completed) without submission
          */
         public function markHomeworkAsDone($authCode, $detailId) {
             $device = MobileDevice::where('auth_code', $authCode)->first();

             if (!$device) {
                 return response()->json(['error' => 'Invalid authentication code'], 401);
             }

             // Find the homework detail record for this student
             $homeworkDetail = HomeworkDetail::where('detail_id', $detailId)
                 ->where('student_id', $device->student_id)
                 ->first();

             if (!$homeworkDetail) {
                 return response()->json(['error' => 'Homework not found or access denied'], 404);
             }

             try {
                 \DB::beginTransaction();

                 // Mark as completed
                 $homeworkDetail->is_completed = 1;

                 // Set submission date if not already set
                 if (!$homeworkDetail->sumitted_date) {
                     $homeworkDetail->sumitted_date = now();
                 }

                 // Mark as viewed if not already viewed
                 if (!$homeworkDetail->viewed_at) {
                     $homeworkDetail->viewed_at = now();
                 }

                 $homeworkDetail->save();

                 // Get homework and teacher information for notification
                 $homework = \App\Models\Homework::with(['grade.subject'])->find($homeworkDetail->homework_id);
                 $student = User::find($homeworkDetail->student_id);

                 // Send immediate notification to teacher
                 $notificationSent = false;
                 $notificationMessage = '';
                 try {
                     // Get subject information for better notification context
                     $subject = $homework->grade->subject ?? null;
                     $subjectName = $subject ? $subject->subject_name : 'Unknown Subject';

                     $notificationData = [
                         'student' => $homework->user_id, // Send to teacher
                         'type' => 'homework_completed',
                         'title' => 'Homework Completed',
                         'message' => "Student {$student->name} has marked homework as done: {$homework->title} ({$subjectName})",
                         'user_type' => 'staff',
                         'priority' => 'normal',
                         'category' => 'homework',
                         'data' => [
                             'homework_id' => $homework->homework_id,
                             'homework_title' => $homework->title,
                             'subject_name' => $subjectName,
                             'subject_id' => $subject ? $subject->subject_id : null,
                             'student_name' => $student->name,
                             'student_id' => $student->id,
                             'completion_type' => 'homework_completed'
                         ]
                     ];

                     $this->mobileNotification->sendRealTime($notificationData);
                     $notificationSent = true;
                     $notificationMessage = 'Teacher notification sent successfully';
                 } catch (\Exception $e) {
                     // Don't fail homework completion if notification fails
                     $notificationSent = false;
                     $notificationMessage = 'Notification error: ' . $e->getMessage();
                 }

                 \DB::commit();

                 return response()->json([
                     'success' => true,
                     'message' => 'Homework marked as done',
                     'data' => [
                         'detail_id' => $homeworkDetail->detail_id,
                         'is_completed' => $homeworkDetail->is_completed,
                         'submitted_date' => $homeworkDetail->sumitted_date,
                         'viewed_at' => $homeworkDetail->viewed_at
                     ],
                     'notification' => [
                         'sent' => $notificationSent,
                         'message' => $notificationMessage
                     ]
                 ]);

             } catch (\Exception $e) {
                 \DB::rollBack();
                 return response()->json(['error' => 'Failed to mark homework as done'], 500);
             }
         }

         /**
          * Upload file for homework submission
          */
         public function uploadHomeworkFile($authCode, $file) {
             $device = MobileDevice::where('auth_code', $authCode)->first();

             if (!$device) {
                 return response()->json(['error' => 'Invalid authentication code'], 401);
             }

             // Validate file
             if (!$file || !$file->isValid()) {
                 return response()->json(['error' => 'Invalid file'], 400);
             }

             // Check file size (max 10MB for homework files)
             if ($file->getSize() > 10 * 1024 * 1024) {
                 return response()->json(['error' => 'File size must not exceed 10MB'], 400);
             }

             // Check file type
             $allowedMimes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'jpeg', 'jpg', 'png', 'gif', 'mp4', 'mov', 'avi'];
             $fileExtension = strtolower($file->getClientOriginalExtension());

             if (!in_array($fileExtension, $allowedMimes)) {
                 return response()->json([
                     'error' => 'Invalid file type. Allowed types: ' . implode(', ', $allowedMimes)
                 ], 400);
             }

             try {
                 // Create unique directory for homework files
                 $uid = uniqid('hw_', true);
                 $studentId = $device->student_id;
                 $filePath = "data/homework_submissions/{$studentId}/{$uid}";

                 // Create directory if it doesn't exist
                 if (!\File::exists(public_path($filePath))) {
                     \File::makeDirectory(public_path($filePath), 0755, true);
                 }

                 // Generate safe filename
                 $originalName = $file->getClientOriginalName();
                 $safeName = time() . '_' . preg_replace('/[^a-zA-Z0-9._-]/', '_', $originalName);
                 $fullPath = $filePath . '/' . $safeName;

                 // Move file to destination
                 $file->move(public_path($filePath), $safeName);

                 // Return file information
                 return response()->json([
                     'success' => true,
                     'message' => 'File uploaded successfully',
                     'data' => [
                         'file_path' => $fullPath,
                         'file_name' => $originalName,
                         'file_size' => $file->getSize(),
                         'file_type' => $fileExtension,
                         'upload_time' => now(),
                         'file_url' => url($fullPath) // Full URL for accessing the file
                     ]
                 ]);

             } catch (\Exception $e) {
                 return response()->json(['error' => 'Failed to upload file'], 500);
             }
         }

         /**
          * Update/resubmit homework after teacher feedback or rejection
          */
         public function updateHomeworkSubmission($authCode, $data) {
             $device = MobileDevice::where('auth_code', $authCode)->first();

             if (!$device) {
                 return response()->json(['error' => 'Invalid authentication code'], 401);
             }

             // Validate required fields
             if (!isset($data['detail_id'])) {
                 return response()->json(['error' => 'Homework detail ID is required'], 400);
             }

             // Find the homework detail record for this student
             $homeworkDetail = HomeworkDetail::where('detail_id', $data['detail_id'])
                 ->where('student_id', $device->student_id)
                 ->first();

             if (!$homeworkDetail) {
                 return response()->json(['error' => 'Homework not found or access denied'], 404);
             }

             // Check if homework has teacher feedback (indicating it was reviewed)
             if (empty($homeworkDetail->teacher_comment)) {
                 return response()->json(['error' => 'Homework can only be updated after teacher feedback'], 400);
             }

             // Check if homework is approved - approved homework cannot be updated
             if (isset($homeworkDetail->approval_status) && $homeworkDetail->approval_status === 'approved') {
                 return response()->json(['error' => 'Cannot update approved homework'], 400);
             }

             // Only allow updates for rejected homework or homework without approval status
             if (isset($homeworkDetail->approval_status) && $homeworkDetail->approval_status !== 'rejected') {
                 return response()->json(['error' => 'Homework can only be updated when rejected by teacher'], 400);
             }

             try {
                 \DB::beginTransaction();

                 // Store previous submission for reference
                 $previousReplyFile = $homeworkDetail->reply_file;
                 $previousReplyData = $homeworkDetail->reply_data;

                 // Update homework detail with new submission data
                 if (isset($data['reply_file'])) {
                     $homeworkDetail->reply_file = $data['reply_file'];
                 }

                 if (isset($data['reply_data'])) {
                     $homeworkDetail->reply_data = $data['reply_data'];
                 }

                 // Mark as completed if there's any submission content
                 if (!empty($data['reply_file']) || !empty($data['reply_data'])) {
                     $homeworkDetail->is_completed = 1;
                 }

                 // Update submission date to current time for resubmission
                 $homeworkDetail->sumitted_date = now();

                 // Mark as viewed if not already viewed
                 if (!$homeworkDetail->viewed_at) {
                     $homeworkDetail->viewed_at = now();
                 }

                 // Clear teacher comment to indicate new submission needs review
                 // (Optional: you might want to keep the comment for history)
                 // $homeworkDetail->teacher_comment = null;

                 $homeworkDetail->save();

                 // Get homework and teacher information for notification
                 $homework = \App\Models\Homework::find($homeworkDetail->homework_id);
                 $student = User::find($homeworkDetail->student_id);

                 // Send immediate notification to teacher about resubmission
                 $notificationSent = false;
                 $notificationMessage = '';
                 try {
                     $notificationData = [
                         'student' => $homework->user_id, // Send to teacher
                         'type' => 'homework_resubmitted',
                         'title' => 'Homework Resubmitted',
                         'message' => "Student {$student->name} has resubmitted homework after feedback: {$homework->title}",
                         'user_type' => 'staff',
                         'priority' => 'normal',
                         'category' => 'homework'
                     ];

                     $this->mobileNotification->sendRealTime($notificationData);
                     $notificationSent = true;
                     $notificationMessage = 'Teacher notification sent successfully';
                 } catch (\Exception $e) {
                     // Don't fail homework update if notification fails
                     $notificationSent = false;
                     $notificationMessage = 'Notification error: ' . $e->getMessage();
                 }

                 \DB::commit();

                 return response()->json([
                     'success' => true,
                     'message' => 'Homework updated and resubmitted successfully',
                     'data' => [
                         'detail_id' => $homeworkDetail->detail_id,
                         'is_completed' => $homeworkDetail->is_completed,
                         'submitted_date' => $homeworkDetail->sumitted_date,
                         'viewed_at' => $homeworkDetail->viewed_at,
                         'reply_file' => $homeworkDetail->reply_file,
                         'reply_data' => $homeworkDetail->reply_data,
                         'teacher_comment' => $homeworkDetail->teacher_comment,
                         'previous_submission' => [
                             'reply_file' => $previousReplyFile,
                             'reply_data' => $previousReplyData
                         ]
                     ],
                     'notification' => [
                         'sent' => $notificationSent,
                         'message' => $notificationMessage
                     ]
                 ]);

             } catch (\Exception $e) {
                 \DB::rollBack();
                 return response()->json(['error' => 'Failed to update homework'], 500);
             }
         }

         /**
          * Get teacher's homework list for mobile
          */
         public function getTeacherHomeworkList($authCode) {
             $device = MobileDevice::where('auth_code', $authCode)->first();

             if (!$device) {
                 return response()->json(['error' => 'Invalid authentication code'], 401);
             }

             // Check if device is for staff
             if ($device->user_type !== 'staff') {
                 return response()->json(['error' => 'Access denied. Staff authentication required'], 403);
             }

             $teacherId = $device->student_id; // This is actually staff ID for teacher devices

             // Get branch ID for mobile API context
             $branchId = $this->ah->mobileCurrentBranch($teacherId);
             if (!$branchId) {
                 return response()->json(['error' => 'Branch information not found for teacher'], 404);
             }

             $academicYearId = $this->ah->branchAcademicYear($branchId);

             try {
                 // Get active homework assigned by this teacher with folder information
                 $homeworkList = \App\Models\Homework::with(['grade.subject', 'details.student', 'homeworkFolder'])
                     ->where('user_id', $teacherId)
                     ->where('academic_year_id', $academicYearId)
                     ->where('homework_status', 1)
                     ->orderBy('created_at', 'desc')
                     ->get();

                 $formattedHomework = [];
                 foreach ($homeworkList as $homework) {
                     $totalStudents = $homework->details->count();
                     $submittedCount = $homework->details->where('is_completed', 1)->count();
                     $pendingCount = $totalStudents - $submittedCount;
                     $submissionRate = $totalStudents > 0 ? round(($submittedCount / $totalStudents) * 100, 1) : 0;

                     // Parse Google Drive files if available
                     $googleDriveFiles = null;
                     if (!empty($homework->google_drive_files)) {
                         $googleDriveFiles = json_decode($homework->google_drive_files, true);
                     }

                     $formattedHomework[] = [
                         'homework_id' => $homework->homework_id,
                         'homework_uid' => $homework->homework_uid,
                         'title' => $homework->title,
                         'subject_name' => $homework->grade->subject->subject_name ?? 'Unknown Subject',
                         'grade_name' => $homework->grade->grade_name ?? 'Unknown Grade',
                         'homework_data' => $homework->homework_data,
                         'homework_files' => $homework->homework_files,
                         'homework_video_links' => $homework->homework_video_links,
                         'deadline' => $homework->deadline,
                         'created_at' => $homework->created_at->format('Y-m-d H:i:s'),
                         // Google Drive integration fields
                         'google_drive_folder_id' => $homework->google_drive_folder_id,
                         'google_drive_files' => $googleDriveFiles,
                         'folder_info' => [
                             'folder_name' => $homework->homeworkFolder->folder_name ?? null,
                             'folder_description' => $homework->homeworkFolder->description ?? null,
                             'has_folder' => !empty($homework->google_drive_folder_id)
                         ],
                         'statistics' => [
                             'total_students' => $totalStudents,
                             'submitted_count' => $submittedCount,
                             'pending_count' => $pendingCount,
                             'submission_rate' => $submissionRate
                         ],
                         'status' => $homework->deadline < now() ? 'overdue' : 'active',
                         'has_google_drive_integration' => !empty($homework->google_drive_folder_id),
                         'has_teacher_files' => !empty($homework->homework_files) || !empty($googleDriveFiles)
                     ];
                 }

                 return response()->json([
                     'success' => true,
                     'data' => $formattedHomework,
                     'total_count' => count($formattedHomework)
                 ]);

             } catch (\Exception $e) {
                 return response()->json(['error' => 'Failed to retrieve homework list'], 500);
             }
         }

         /**
          * Get homework details with student submissions for teacher review
          */
         public function getTeacherHomeworkDetails($authCode, $homeworkId) {
             $device = MobileDevice::where('auth_code', $authCode)->first();

             if (!$device) {
                 return response()->json(['error' => 'Invalid authentication code'], 401);
             }

             // Check if device is for staff
             if ($device->user_type !== 'staff') {
                 return response()->json(['error' => 'Access denied. Staff authentication required'], 403);
             }

             $teacherId = $device->student_id;

             try {
                 // Get homework with details
                 $homework = \App\Models\Homework::with(['grade.subject', 'details.student'])
                     ->where('homework_id', $homeworkId)
                     ->where('user_id', $teacherId) // Ensure teacher owns this homework
                     ->first();

                 if (!$homework) {
                     return response()->json(['error' => 'Homework not found or access denied'], 404);
                 }

                 // Format student submissions
                 $submissions = [];
                 foreach ($homework->details as $detail) {
                     // Get Google Drive submissions for this student
                     $googleDriveSubmissions = \App\Models\HomeworkSubmission::where('homework_id', $homework->homework_id)
                         ->where('student_id', $detail->student_id)
                         ->orderBy('submitted_at', 'desc')
                         ->get()
                         ->map(function($submission) {
                             return [
                                 'id' => $submission->id,
                                 'google_drive_file_id' => $submission->google_drive_file_id,
                                 'original_filename' => $submission->original_filename,
                                 'file_type' => $submission->file_type,
                                 'file_size' => $submission->file_size,
                                 'formatted_size' => $submission->formatted_size,
                                 'web_view_link' => $submission->web_view_link,
                                 'web_content_link' => $submission->web_content_link,
                                 'submission_note' => $submission->submission_note,
                                 'submitted_at' => $submission->submitted_at->format('Y-m-d H:i:s'),
                                 'is_image' => $submission->isImage(),
                                 'is_document' => $submission->isDocument(),
                                 'file_extension' => $submission->file_extension
                             ];
                         })
                         ->toArray();

                     $submissions[] = [
                         'detail_id' => $detail->detail_id,
                         'student_id' => $detail->student_id,
                         'student_name' => $detail->student->name ?? 'Unknown Student',
                         'student_photo' => $detail->student->photo ? "https://sis.bfi.edu.mm" . $detail->student->photo : null,
                         'is_completed' => $detail->is_completed == 1,
                         'reply_data' => $detail->reply_data,
                         'reply_file' => $detail->reply_file,
                         'teacher_comment' => $detail->teacher_comment,
                         'approval_status' => $detail->approval_status ?? null,
                         'reviewed_at' => $detail->reviewed_at ?? null,
                         'viewed_at' => $detail->viewed_at,
                         'submitted_date' => $detail->sumitted_date,
                         'submission_status' => $this->getSubmissionStatus($detail),
                         'needs_review' => $detail->is_completed == 1 && empty($detail->teacher_comment),
                         'can_be_updated' => $this->canHomeworkBeUpdated($detail),
                         'google_drive_submissions' => $googleDriveSubmissions,
                         'has_google_drive_submissions' => count($googleDriveSubmissions) > 0
                     ];
                 }

                 // Sort submissions: needs review first, then by submission date
                 usort($submissions, function($a, $b) {
                     if ($a['needs_review'] && !$b['needs_review']) return -1;
                     if (!$a['needs_review'] && $b['needs_review']) return 1;
                     return strcmp($b['submitted_date'] ?? '', $a['submitted_date'] ?? '');
                 });

                 $totalStudents = count($submissions);
                 $submittedCount = count(array_filter($submissions, fn($s) => $s['is_completed']));
                 $needsReviewCount = count(array_filter($submissions, fn($s) => $s['needs_review']));

                 return response()->json([
                     'success' => true,
                     'data' => [
                         'homework' => [
                             'homework_id' => $homework->homework_id,
                             'homework_uid' => $homework->homework_uid,
                             'title' => $homework->title,
                             'subject_name' => $homework->grade->subject->subject_name ?? 'Unknown Subject',
                             'grade_name' => $homework->grade->grade_name ?? 'Unknown Grade',
                             'homework_data' => $homework->homework_data,
                             'homework_files' => $homework->homework_files,
                             'homework_video_links' => $homework->homework_video_links,
                             'deadline' => $homework->deadline,
                             'created_at' => $homework->created_at->format('Y-m-d H:i:s'),
                             'google_drive_folder_id' => $homework->google_drive_folder_id,
                             'google_drive_files' => !empty($homework->google_drive_files)
                                 ? (is_string($homework->google_drive_files)
                                     ? json_decode($homework->google_drive_files, true)
                                     : $homework->google_drive_files)
                                 : [],
                             'has_google_drive_integration' => !empty($homework->google_drive_folder_id)
                         ],
                         'submissions' => $submissions,
                         'statistics' => [
                             'total_students' => $totalStudents,
                             'submitted_count' => $submittedCount,
                             'pending_count' => $totalStudents - $submittedCount,
                             'needs_review_count' => $needsReviewCount,
                             'submission_rate' => $totalStudents > 0 ? round(($submittedCount / $totalStudents) * 100, 1) : 0
                         ]
                     ]
                 ]);

             } catch (\Exception $e) {
                 return response()->json(['error' => 'Failed to retrieve homework details'], 500);
             }
         }

         /**
          * Helper method to determine submission status
          */
         private function getSubmissionStatus($detail) {
             if (!$detail->is_completed) {
                 return 'not_submitted';
             }

             if (empty($detail->teacher_comment)) {
                 return 'needs_review';
             }

             // Check approval status if available
             if (isset($detail->approval_status)) {
                 if ($detail->approval_status === 'approved') {
                     return 'approved';
                 } elseif ($detail->approval_status === 'rejected') {
                     return 'rejected';
                 }
             }

             return 'reviewed';
         }

         /**
          * Helper method to determine if homework can be updated by student
          */
         private function canHomeworkBeUpdated($detail) {
             // Not submitted homework cannot be updated through this method
             if (!$detail->is_completed) {
                 return false;
             }

             // No teacher feedback means it hasn't been reviewed yet
             if (empty($detail->teacher_comment)) {
                 return false;
             }

             // Approved homework cannot be updated
             if (isset($detail->approval_status) && $detail->approval_status === 'approved') {
                 return false;
             }

             // Rejected homework can be updated
             if (isset($detail->approval_status) && $detail->approval_status === 'rejected') {
                 return true;
             }

             // Homework with feedback but no approval status can be updated (backward compatibility)
             return true;
         }

         /**
          * Approve or reject homework submission
          */
         public function reviewHomeworkSubmission($authCode, $data) {
             $device = MobileDevice::where('auth_code', $authCode)->first();

             if (!$device) {
                 return response()->json(['error' => 'Invalid authentication code'], 401);
             }

             // Check if device is for staff
             if ($device->user_type !== 'staff') {
                 return response()->json(['error' => 'Access denied. Staff authentication required'], 403);
             }

             // Validate required fields
             if (!isset($data['detail_id']) || !isset($data['action'])) {
                 return response()->json(['error' => 'Detail ID and action are required'], 400);
             }

             // Validate action
             if (!in_array($data['action'], ['approve', 'reject'])) {
                 return response()->json(['error' => 'Invalid action. Must be "approve" or "reject"'], 400);
             }

             // Comment is required for rejection
             if ($data['action'] === 'reject' && empty($data['comment'])) {
                 return response()->json(['error' => 'Comment is required when rejecting homework'], 400);
             }

             $teacherId = $device->student_id;

             try {
                 \DB::beginTransaction();

                 // Get homework detail and verify teacher ownership
                 $homeworkDetail = HomeworkDetail::with('homework')
                     ->where('detail_id', $data['detail_id'])
                     ->first();

                 if (!$homeworkDetail) {
                     return response()->json(['error' => 'Homework submission not found'], 404);
                 }

                 // Verify teacher owns this homework
                 if ($homeworkDetail->homework->user_id != $teacherId) {
                     return response()->json(['error' => 'Access denied. You can only review your own homework assignments'], 403);
                 }

                 // Check if homework is submitted
                 if (!$homeworkDetail->is_completed) {
                     return response()->json(['error' => 'Cannot review homework that has not been submitted'], 400);
                 }

                 // Update approval status and comment
                 $homeworkDetail->approval_status = $data['action'] === 'approve' ? 'approved' : 'rejected';
                 $homeworkDetail->reviewed_at = now();

                 // Update comment if provided
                 if (!empty($data['comment'])) {
                     $homeworkDetail->teacher_comment = $data['comment'];
                 }

                 $homeworkDetail->save();

                 // Get student information for notification
                 $student = User::find($homeworkDetail->student_id);
                 $homework = $homeworkDetail->homework;

                 // Send immediate notification to student
                 $notificationSent = false;
                 $notificationMessage = '';
                 try {
                     $notificationTitle = $data['action'] === 'approve' ? 'Homework Approved' : 'Homework Needs Revision';
                     $notificationBody = $data['action'] === 'approve'
                         ? "Your homework has been approved: {$homework->title}"
                         : "Your homework needs revision: {$homework->title}. Please check the feedback and resubmit.";

                     $notificationData = [
                         'student' => $homeworkDetail->student_id,
                         'type' => 'homework_review',
                         'title' => $notificationTitle,
                         'message' => $notificationBody,
                         'user_type' => 'student',
                         'priority' => 'normal',
                         'category' => 'homework',
                         'approval_status' => $homeworkDetail->approval_status
                     ];

                     $this->mobileNotification->sendRealTime($notificationData);
                     $notificationSent = true;
                     $notificationMessage = 'Student notification sent successfully';
                 } catch (\Exception $e) {
                     // Don't fail review if notification fails
                     $notificationSent = false;
                     $notificationMessage = 'Notification error: ' . $e->getMessage();
                 }

                 \DB::commit();

                 return response()->json([
                     'success' => true,
                     'message' => 'Homework ' . ($data['action'] === 'approve' ? 'approved' : 'rejected') . ' successfully',
                     'data' => [
                         'detail_id' => $homeworkDetail->detail_id,
                         'approval_status' => $homeworkDetail->approval_status,
                         'reviewed_at' => $homeworkDetail->reviewed_at,
                         'teacher_comment' => $homeworkDetail->teacher_comment,
                         'student_name' => $student->name,
                         'homework_title' => $homework->title
                     ],
                     'notification' => [
                         'sent' => $notificationSent,
                         'message' => $notificationMessage
                     ]
                 ]);

             } catch (\Exception $e) {
                 \DB::rollBack();
                 return response()->json(['error' => 'Failed to review homework'], 500);
             }
         }

         /**
          * Provide teacher feedback on student homework submission with approval/rejection
          */
         public function provideHomeworkFeedback($authCode, $data) {
             $device = MobileDevice::where('auth_code', $authCode)->first();

             if (!$device) {
                 return response()->json(['error' => 'Invalid authentication code'], 401);
             }

             // Check if device is for staff
             if ($device->user_type !== 'staff') {
                 return response()->json(['error' => 'Access denied. Staff authentication required'], 403);
             }

             // Validate required fields
             if (!isset($data['detail_id']) || !isset($data['comment'])) {
                 return response()->json(['error' => 'Detail ID and comment are required'], 400);
             }

             // Validate approval status if provided
             $approvalStatus = $data['approval_status'] ?? null;
             if ($approvalStatus && !in_array($approvalStatus, ['approved', 'rejected'])) {
                 return response()->json(['error' => 'Invalid approval status. Must be "approved" or "rejected"'], 400);
             }

             $teacherId = $device->student_id;

             try {
                 \DB::beginTransaction();

                 // Get homework detail and verify teacher ownership
                 $homeworkDetail = HomeworkDetail::with('homework')
                     ->where('detail_id', $data['detail_id'])
                     ->first();

                 if (!$homeworkDetail) {
                     return response()->json(['error' => 'Homework submission not found'], 404);
                 }

                 // Verify teacher owns this homework
                 if ($homeworkDetail->homework->user_id != $teacherId) {
                     return response()->json(['error' => 'Access denied. You can only review your own homework assignments'], 403);
                 }

                 // Update teacher comment and approval status
                 $homeworkDetail->teacher_comment = $data['comment'];

                 // Set approval status if provided
                 if ($approvalStatus) {
                     $homeworkDetail->approval_status = $approvalStatus;
                     $homeworkDetail->reviewed_at = now();
                 }

                 $homeworkDetail->save();

                 // Get student information for notification
                 $student = User::find($homeworkDetail->student_id);
                 $homework = $homeworkDetail->homework;

                 // Send immediate notification to student
                 $notificationSent = false;
                 $notificationMessage = '';
                 try {
                     // Create notification based on approval status
                     $notificationTitle = 'Homework Feedback Received';
                     $notificationMessage = "You have received feedback on your homework: {$homework->title}";

                     if ($approvalStatus === 'approved') {
                         $notificationTitle = 'Homework Approved';
                         $notificationMessage = "Your homework has been approved: {$homework->title}";
                     } elseif ($approvalStatus === 'rejected') {
                         $notificationTitle = 'Homework Needs Revision';
                         $notificationMessage = "Your homework needs revision: {$homework->title}. Please check the feedback and resubmit.";
                     }

                     $notificationData = [
                         'student' => $homeworkDetail->student_id,
                         'type' => 'homework_feedback',
                         'title' => $notificationTitle,
                         'message' => $notificationMessage,
                         'user_type' => 'student',
                         'priority' => 'normal',
                         'category' => 'homework',
                         'approval_status' => $approvalStatus
                     ];

                     $this->mobileNotification->sendRealTime($notificationData);
                     $notificationSent = true;
                     $notificationMessage = 'Student notification sent successfully';
                 } catch (\Exception $e) {
                     // Don't fail feedback submission if notification fails
                     $notificationSent = false;
                     $notificationMessage = 'Notification error: ' . $e->getMessage();
                 }

                 \DB::commit();

                 return response()->json([
                     'success' => true,
                     'message' => 'Feedback provided successfully',
                     'data' => [
                         'detail_id' => $homeworkDetail->detail_id,
                         'teacher_comment' => $homeworkDetail->teacher_comment,
                         'approval_status' => $homeworkDetail->approval_status,
                         'reviewed_at' => $homeworkDetail->reviewed_at,
                         'student_name' => $student->name,
                         'homework_title' => $homework->title
                     ],
                     'notification' => [
                         'sent' => $notificationSent,
                         'message' => $notificationMessage
                     ]
                 ]);

             } catch (\Exception $e) {
                 \DB::rollBack();
                 return response()->json(['error' => 'Failed to provide feedback'], 500);
             }
         }

         /**
          * Create new homework assignment for mobile
          */
         public function createHomeworkAssignment($authCode, $data) {
             $device = MobileDevice::where('auth_code', $authCode)->first();

             if (!$device) {
                 return response()->json(['error' => 'Invalid authentication code'], 401);
             }

             // Check if device is for staff
             if ($device->user_type !== 'staff') {
                 return response()->json(['error' => 'Access denied. Staff authentication required'], 403);
             }

             // Validate required fields
             $requiredFields = ['title', 'grade_id', 'students', 'deadline'];
             foreach ($requiredFields as $field) {
                 if (!isset($data[$field]) || empty($data[$field])) {
                     return response()->json(['error' => "Field '{$field}' is required"], 400);
                 }
             }

             // Validate students array
             if (!is_array($data['students']) || count($data['students']) == 0) {
                 return response()->json(['error' => 'At least one student must be selected'], 400);
             }

             $teacherId = $device->student_id;

             // Get branch ID for mobile API context
             $branchId = $this->ah->mobileCurrentBranch($teacherId);
             if (!$branchId) {
                 return response()->json(['error' => 'Branch information not found for teacher'], 404);
             }

             $academicYearId = $this->ah->branchAcademicYear($branchId);

             try {
                 \DB::beginTransaction();

                 // Get grade information
                 $grade = \App\Models\ElectiveGrade::with('subject')->find($data['grade_id']);
                 if (!$grade) {
                     return response()->json(['error' => 'Invalid grade selected'], 400);
                 }

                 // Create Google Drive folder for homework if requested
                 $googleDriveFolderId = null;
                 $googleDriveFiles = null;

                 if (isset($data['create_google_drive_folder']) && $data['create_google_drive_folder'] && $this->driveRepository) {
                     $folderName = $data['title'] . ' - ' . $grade->grade_name;

                     // Determine assignment type and targets based on student selection
                     $assignmentType = 'mixed'; // Default to mixed for individual assignments
                     $assignedClasses = [];
                     $assignedStudents = $data['students']; // Use the actual selected students

                     // Check if all students from the grade are selected (class assignment)
                     $allGradeStudents = \DB::table('academic_elective_grade_students')
                         ->where('grade_id', $data['grade_id'])
                         ->pluck('student_id')
                         ->toArray();

                     // If all students from the grade are selected, treat as class assignment
                     if (count($data['students']) === count($allGradeStudents) &&
                         empty(array_diff($data['students'], $allGradeStudents))) {
                         $assignmentType = 'class';
                         $assignedClasses = [$data['grade_id']];
                         $assignedStudents = []; // Empty for class assignments
                     }

                     $folderResult = $this->driveRepository->createHomeworkFolder(
                         $teacherId,
                         $folderName,
                         "Homework folder for: " . $data['title'],
                         $assignmentType,
                         $assignedClasses,
                         $assignedStudents
                     );

                     if ($folderResult['success']) {
                         $googleDriveFolderId = $folderResult['drive_folder']['id'];
                     }
                 }

                 // Handle multiple file uploads to Google Drive if folder exists
                 if ($googleDriveFolderId && isset($data['homework_files']) && $data['homework_files'] && $this->driveRepository) {
                     $uploadedFiles = [];

                     // Handle multiple files
                     if (is_array($data['homework_files'])) {
                         // Filter out non-file objects
                         $validFiles = array_filter($data['homework_files'], function($file) {
                             return is_object($file) && method_exists($file, 'getClientOriginalName');
                         });

                         if (!empty($validFiles)) {
                             $multipleUploadResult = $this->driveRepository->uploadMultipleHomeworkFiles(
                                 $validFiles,
                                 $googleDriveFolderId,
                                 "Homework file for: " . $data['title'],
                                 $teacherId  // Pass teacher ID for uploaded_by field
                             );

                             if ($multipleUploadResult['success'] && !empty($multipleUploadResult['uploaded_files'])) {
                                 foreach ($multipleUploadResult['uploaded_files'] as $fileResult) {
                                     $uploadedFiles[] = [
                                         'file_id' => $fileResult['drive_file']['id'],
                                         'file_name' => $fileResult['file']->file_name,
                                         'original_name' => $fileResult['file']->original_name,
                                         'web_view_link' => $fileResult['file']->web_view_link,
                                         'file_size' => $fileResult['file']->file_size
                                     ];
                                 }
                             }

                             // Log any upload errors
                             if (!empty($multipleUploadResult['errors'])) {
                                 \Log::warning('Some homework files failed to upload', [
                                     'homework_title' => $data['title'],
                                     'errors' => $multipleUploadResult['errors']
                                 ]);
                             }
                         }
                     }
                     // Handle single file (backward compatibility)
                     elseif (is_object($data['homework_files']) && method_exists($data['homework_files'], 'getClientOriginalName')) {
                         $fileResult = $this->driveRepository->uploadHomeworkFile(
                             $data['homework_files'],
                             $googleDriveFolderId,
                             "Homework file for: " . $data['title'],
                             $teacherId  // Pass teacher ID for uploaded_by field
                         );

                         if ($fileResult['success']) {
                             $uploadedFiles[] = [
                                 'file_id' => $fileResult['drive_file']['id'],
                                 'file_name' => $fileResult['file']->file_name,
                                 'original_name' => $fileResult['file']->original_name,
                                 'web_view_link' => $fileResult['file']->web_view_link,
                                 'file_size' => $fileResult['file']->file_size
                             ];
                         }
                     }

                     if (!empty($uploadedFiles)) {
                         $googleDriveFiles = json_encode($uploadedFiles);
                     }
                 }

                 // Create homework record
                 $homework = \App\Models\Homework::create([
                     'academic_year_id' => $academicYearId,
                     'branch_id' => $branchId,
                     'user_id' => $teacherId,
                     'title' => $data['title'],
                     'homework_uid' => \Illuminate\Support\Str::uuid(),
                     'grade_id' => $data['grade_id'],
                     'homework_data' => $data['homework_data'] ?? null,
                     'homework_files' => $googleDriveFiles ? null : ($data['homework_files'] ?? null), // Use traditional files if no Google Drive
                     'homework_video_links' => $data['homework_video_links'] ?? null,
                     'deadline' => $data['deadline'],
                     'google_drive_folder_id' => $googleDriveFolderId,
                     'google_drive_files' => $googleDriveFiles
                 ]);

                 // Load the grade relationship for subject information
                 $homework->load('grade.subject');

                 // Create homework details for each student
                 $createdDetails = [];
                 $notificationResults = [];

                 foreach ($data['students'] as $studentId) {
                     // Create homework detail
                     $detail = HomeworkDetail::create([
                         'homework_id' => $homework->homework_id,
                         'student_id' => $studentId,
                         'uuid' => \Illuminate\Support\Str::uuid()
                     ]);

                     $createdDetails[] = $detail;

                     // Send notification to student
                     try {
                         $student = User::find($studentId);
                         $teacher = User::find($teacherId);
                         // Get subject information for better notification context
                         $subject = $homework->grade->subject ?? null;
                         $subjectName = $subject ? $subject->subject_name : 'Unknown Subject';

                         $notificationData = [
                             'student' => $studentId,
                             'type' => 'homework_assigned',
                             'title' => 'New Homework Assignment',
                             'message' => "You have been assigned new homework: {$homework->title} ({$subjectName})",
                             'user_type' => 'student',
                             'priority' => 'normal',
                             'category' => 'homework',
                             'data' => [
                                 'homework_id' => $homework->homework_id,
                                 'homework_title' => $homework->title,
                                 'subject_name' => $subjectName,
                                 'subject_id' => $subject ? $subject->subject_id : null,
                                 'teacher_name' => $teacher ? $teacher->name : 'Unknown Teacher',
                                 'teacher_id' => $teacher ? $teacher->id : $teacherId,
                                 'deadline' => $homework->deadline,
                                 'assignment_type' => 'new_homework'
                             ]
                         ];

                         $this->mobileNotification->sendRealTime($notificationData);
                         $notificationResults[] = [
                             'student_id' => $studentId,
                             'student_name' => $student->name ?? 'Unknown',
                             'notification_sent' => true
                         ];
                     } catch (\Exception $e) {
                         $notificationResults[] = [
                             'student_id' => $studentId,
                             'student_name' => $student->name ?? 'Unknown',
                             'notification_sent' => false,
                             'error' => $e->getMessage()
                         ];
                     }
                 }

                 \DB::commit();

                 return response()->json([
                     'success' => true,
                     'message' => 'Homework assignment created successfully',
                     'data' => [
                         'homework_id' => $homework->homework_id,
                         'homework_uid' => $homework->homework_uid,
                         'title' => $homework->title,
                         'grade_name' => $grade->grade_name,
                         'subject_name' => $grade->subject->subject_name ?? 'Subject Not Found',
                         'deadline' => $homework->deadline,
                         'total_students' => count($data['students']),
                         'created_at' => $homework->created_at->format('Y-m-d H:i:s'),
                         'google_drive_folder_id' => $googleDriveFolderId,
                         'google_drive_files' => $googleDriveFiles ? json_decode($googleDriveFiles, true) : null,
                         'has_google_drive_integration' => !is_null($googleDriveFolderId)
                     ],
                     'notifications' => $notificationResults
                 ]);

             } catch (\Exception $e) {
                 \DB::rollBack();
                 return response()->json(['error' => 'Failed to create homework assignment'], 500);
             }
         }

         /**
          * Get teacher's classes and students for homework assignment
          */
         public function getTeacherClassesForHomework($authCode) {
             $device = MobileDevice::where('auth_code', $authCode)->first();

             if (!$device) {
                 return response()->json(['error' => 'Invalid authentication code'], 401);
             }

             // Check if device is for staff
             if ($device->user_type !== 'staff') {
                 return response()->json(['error' => 'Access denied. Staff authentication required'], 403);
             }

             $teacherId = $device->student_id;

             // Get branch ID for mobile API context
             $branchId = $this->ah->mobileCurrentBranch($teacherId);
             if (!$branchId) {
                 return response()->json(['error' => 'Branch information not found for teacher'], 404);
             }

             $academicYearId = $this->ah->branchAcademicYear($branchId);

             try {
                 // Get teacher's assigned classes/grades through timetable with branch information
                 $teacherGrades = \DB::table('academic_timetable')
                     ->select([
                         'academic_elective_grade.grade_id',
                         'academic_elective_grade.grade_name',
                         'subjects.subject_id',
                         'subjects.subject_name',
                         'academic_timetable.branch_id',
                         'branches.branch_name',
                         'branches.branch_description'
                     ])
                     ->leftJoin('academic_elective_grade', 'academic_elective_grade.grade_id', 'academic_timetable.grade_id')
                     ->leftJoin('subjects', 'subjects.subject_id', 'academic_elective_grade.subject_id')
                     ->leftJoin('branches', 'branches.branch_id', 'academic_timetable.branch_id')
                     ->where('academic_timetable.user_id', $teacherId)
                     ->where('academic_timetable.academic_year_id', $academicYearId)
                     ->where('academic_elective_grade.grade_status', 1)
                     ->groupBy('academic_elective_grade.grade_id', 'academic_elective_grade.grade_name', 'subjects.subject_id', 'subjects.subject_name', 'academic_timetable.branch_id', 'branches.branch_name', 'branches.branch_description')
                     ->orderBy('branches.branch_name')
                     ->orderBy('academic_elective_grade.grade_name')
                     ->get();

                 // Group classes by branches
                 $branchesData = [];
                 $totalClasses = 0;

                 foreach ($teacherGrades as $grade) {
                     $branchId = $grade->branch_id;
                     $branchName = $grade->branch_name ?? 'Unknown Branch';
                     $branchDescription = $grade->branch_description ?? '';

                     // Initialize branch if not exists
                     if (!isset($branchesData[$branchId])) {
                         $branchesData[$branchId] = [
                             'branch_id' => $branchId,
                             'branch_name' => $branchName,
                             'branch_description' => $branchDescription,
                             'classes' => [],
                             'total_classes' => 0
                         ];
                     }

                     // Get students in this grade
                     $students = \App\Models\ElectiveGradeStudent::select([
                             'academic_elective_grade_students.student_id',
                             'users.name as student_name',
                             'users.photo as student_photo'
                         ])
                         ->leftJoin('users', 'users.id', 'academic_elective_grade_students.student_id')
                         ->where('academic_elective_grade_students.grade_id', $grade->grade_id)
                         ->where('users.user_status', 1)
                         ->orderBy('users.name')
                         ->get()
                         ->map(function($student) {
                             return [
                                 'student_id' => $student->student_id,
                                 'student_name' => $student->student_name,
                                 'student_photo' => $student->student_photo ? "https://sis.bfi.edu.mm" . $student->student_photo : null
                             ];
                         })
                         ->toArray();

                     $classData = [
                         'grade_id' => $grade->grade_id,
                         'grade_name' => $grade->grade_name,
                         'subject_id' => $grade->subject_id,
                         'subject_name' => $grade->subject_name,
                         'student_count' => count($students),
                         'students' => $students
                     ];

                     $branchesData[$branchId]['classes'][] = $classData;
                     $branchesData[$branchId]['total_classes']++;
                     $totalClasses++;
                 }

                 // Convert associative array to indexed array for JSON response
                 $formattedBranches = array_values($branchesData);

                 return response()->json([
                     'success' => true,
                     'data' => [
                         'branches' => $formattedBranches,
                         'summary' => [
                             'total_branches' => count($formattedBranches),
                             'total_classes' => $totalClasses
                         ]
                     ]
                 ]);

             } catch (\Exception $e) {
                 return response()->json(['error' => 'Failed to retrieve teacher classes'], 500);
             }
         }

         /**
          * Get teacher's classes and students for attendance taking
          */
         public function getTeacherClassesForAttendance($authCode) {
             $device = MobileDevice::where('auth_code', $authCode)->first();

             if (!$device) {
                 return response()->json(['error' => 'Invalid authentication code'], 401);
             }

             // Check if device is for staff
             if ($device->user_type !== 'staff') {
                 return response()->json(['error' => 'Access denied. Staff authentication required'], 403);
             }

             $teacherId = $device->student_id;

             // Get branch ID for mobile API context
             $branchId = $this->ah->mobileCurrentBranch($teacherId);
             if (!$branchId) {
                 return response()->json(['error' => 'Branch information not found for teacher'], 404);
             }

             $academicYearId = $this->ah->branchAcademicYear($branchId);

             try {
                 // Get teacher's assigned classes/grades through timetable with branch information (same as homework)
                 $teacherGrades = \DB::table('academic_timetable')
                     ->select([
                         'academic_elective_grade.grade_id',
                         'academic_elective_grade.grade_name',
                         'subjects.subject_id',
                         'subjects.subject_name',
                         'academic_timetable.branch_id',
                         'branches.branch_name',
                         'branches.branch_description'
                     ])
                     ->leftJoin('academic_elective_grade', 'academic_elective_grade.grade_id', 'academic_timetable.grade_id')
                     ->leftJoin('subjects', 'subjects.subject_id', 'academic_elective_grade.subject_id')
                     ->leftJoin('branches', 'branches.branch_id', 'academic_timetable.branch_id')
                     ->where('academic_timetable.user_id', $teacherId)
                     ->where('academic_timetable.academic_year_id', $academicYearId)
                     ->where('academic_elective_grade.grade_status', 1)
                     ->groupBy('academic_elective_grade.grade_id', 'academic_elective_grade.grade_name', 'subjects.subject_id', 'subjects.subject_name', 'academic_timetable.branch_id', 'branches.branch_name', 'branches.branch_description')
                     ->orderBy('branches.branch_name')
                     ->orderBy('academic_elective_grade.grade_name')
                     ->get();

                 // Group classes by branches (same structure as homework)
                 $branchesData = [];
                 $totalClasses = 0;

                 foreach ($teacherGrades as $grade) {
                     $branchId = $grade->branch_id;
                     $branchName = $grade->branch_name ?? 'Unknown Branch';
                     $branchDescription = $grade->branch_description ?? '';

                     // Initialize branch if not exists
                     if (!isset($branchesData[$branchId])) {
                         $branchesData[$branchId] = [
                             'branch_id' => $branchId,
                             'branch_name' => $branchName,
                             'branch_description' => $branchDescription,
                             'classes' => [],
                             'total_classes' => 0
                         ];
                     }

                     // Get students in this grade using elective grade students table
                     $students = \App\Models\ElectiveGradeStudent::select([
                             'academic_elective_grade_students.student_id',
                             'users.name as student_name',
                             'users.photo as student_photo'
                         ])
                         ->leftJoin('users', 'users.id', 'academic_elective_grade_students.student_id')
                         ->where('academic_elective_grade_students.grade_id', $grade->grade_id)
                         ->where('users.user_status', 1)
                         ->orderBy('users.name')
                         ->get()
                         ->map(function($student) {
                             return [
                                 'student_id' => $student->student_id,
                                 'student_name' => $student->student_name,
                                 'student_photo' => $student->student_photo ? "https://sis.bfi.edu.mm" . $student->student_photo : null
                             ];
                         })
                         ->toArray();

                     $classData = [
                         'grade_id' => $grade->grade_id,
                         'grade_name' => $grade->grade_name,
                         'subject_id' => $grade->subject_id,
                         'subject_name' => $grade->subject_name,
                         'student_count' => count($students),
                         'students' => $students
                     ];

                     $branchesData[$branchId]['classes'][] = $classData;
                     $branchesData[$branchId]['total_classes']++;
                     $totalClasses++;
                 }

                 // Convert associative array to indexed array for JSON response
                 $formattedBranches = array_values($branchesData);

                 return response()->json([
                     'success' => true,
                     'data' => [
                         'branches' => $formattedBranches,
                         'summary' => [
                             'total_branches' => count($formattedBranches),
                             'total_classes' => $totalClasses
                         ]
                     ]
                 ]);

             } catch (\Exception $e) {
                 return response()->json(['error' => 'Failed to retrieve teacher classes'], 500);
             }
         }

         /**
          * Close/Archive homework assignment(s) - marks homework as inactive instead of deleting
          */
         public function closeHomeworkAssignment($authCode, $homeworkIds) {
             $device = MobileDevice::where('auth_code', $authCode)->first();

             if (!$device) {
                 return response()->json(['error' => 'Invalid authentication code'], 401);
             }

             // Check if device is for staff
             if ($device->user_type !== 'staff') {
                 return response()->json(['error' => 'Access denied. Staff authentication required'], 403);
             }

             $teacherId = $device->student_id; // This is actually staff ID for teacher devices

             // Convert single ID to array for uniform processing
             if (!is_array($homeworkIds)) {
                 $homeworkIds = [$homeworkIds];
             }

             // Validate homework IDs
             if (empty($homeworkIds)) {
                 return response()->json(['error' => 'No homework IDs provided'], 400);
             }

             try {
                 $results = [
                     'success' => [],
                     'failed' => []
                 ];

                 \DB::beginTransaction();

                 foreach ($homeworkIds as $homeworkId) {
                     // Find the homework and verify ownership
                     $homework = \App\Models\Homework::where('homework_id', $homeworkId)
                         ->where('user_id', $teacherId)
                         ->first();

                     if (!$homework) {
                         $results['failed'][] = [
                             'homework_id' => $homeworkId,
                             'error' => 'Homework not found or you do not have permission to close it'
                         ];
                         continue;
                     }

                     // Check if homework is already closed (homework_status = 1 means active, != 1 means inactive)
                     if ($homework->homework_status != 1) {
                         $results['failed'][] = [
                             'homework_id' => $homeworkId,
                             'error' => 'Homework is already closed',
                             'title' => $homework->title
                         ];
                         continue;
                     }

                     // Count student submissions for information
                     $submittedCount = \App\Models\HomeworkDetail::where('homework_id', $homeworkId)
                         ->where(function($query) {
                             $query->whereNotNull('reply_file')
                                   ->orWhereNotNull('reply_data')
                                   ->orWhere('is_completed', 1);
                         })
                         ->count();

                     // Mark homework as inactive (closed) instead of deleting
                     $homework->homework_status = 0; // 0 = inactive, 1 = active
                     $homework->save();

                     $successEntry = [
                         'homework_id' => $homeworkId,
                         'title' => $homework->title,
                         'closed_at' => now()->toDateTimeString(),
                         'status' => 'closed'
                     ];

                     // Add submission info for reference
                     if ($submittedCount > 0) {
                         $successEntry['student_submissions_preserved'] = $submittedCount;
                         $successEntry['note'] = "Homework closed successfully. All {$submittedCount} student submissions are preserved.";
                     } else {
                         $successEntry['note'] = "Homework closed successfully. No student submissions to preserve.";
                     }

                     $results['success'][] = $successEntry;
                 }

                 \DB::commit();

                 // Determine overall response
                 $totalRequested = count($homeworkIds);
                 $successCount = count($results['success']);
                 $failedCount = count($results['failed']);

                 $response = [
                     'success' => $successCount > 0,
                     'summary' => [
                         'total_requested' => $totalRequested,
                         'successfully_closed' => $successCount,
                         'failed' => $failedCount
                     ],
                     'results' => $results
                 ];

                 if ($successCount === $totalRequested) {
                     $response['message'] = $totalRequested === 1 ?
                         'Homework closed successfully' :
                         "All {$totalRequested} homework assignments closed successfully";
                 } elseif ($successCount > 0) {
                     $response['message'] = "{$successCount} out of {$totalRequested} homework assignments closed successfully";
                 } else {
                     $response['message'] = 'No homework assignments were closed';
                 }

                 return response()->json($response);

             } catch (\Exception $e) {
                 \DB::rollBack();
                 return response()->json(['error' => 'Failed to delete homework'], 500);
             }
         }

         /**
          * Get homeroom teacher's assigned classrooms
          */
         public function getHomeroomClassrooms($authCode) {
             $device = MobileDevice::where('auth_code', $authCode)->first();

             if (!$device) {
                 return response()->json(['error' => 'Invalid authentication code'], 401);
             }

             // Check if device is for staff
             if ($device->user_type !== 'staff') {
                 return response()->json(['error' => 'Access denied. Staff authentication required'], 403);
             }

             $teacherId = $device->student_id; // This is actually staff ID for teacher devices

             // Get branch ID for mobile API context
             $branchId = $this->ah->mobileCurrentBranch($teacherId);
             if (!$branchId) {
                 return response()->json(['error' => 'Branch information not found for teacher'], 404);
             }

             $academicYearId = $this->ah->branchAcademicYear($branchId);

             try {
                 // Get homeroom classrooms assigned to this teacher
                 $classrooms = \App\Models\Classroom::with(['students.student'])
                     ->where('homeroom_teacher_id', $teacherId)
                     ->where('academic_year_id', $academicYearId)
                     ->where('status', 1)
                     ->get();

                 $formattedClassrooms = [];
                 foreach ($classrooms as $classroom) {
                     $totalStudents = $classroom->students->count();
                     $maleCount = $classroom->students->filter(function($studentClassroom) {
                         return $studentClassroom->student && strtolower($studentClassroom->student->gender) === 'male';
                     })->count();
                     $femaleCount = $classroom->students->filter(function($studentClassroom) {
                        return $studentClassroom->student && strtolower($studentClassroom->student->gender) === 'female';
                    })->count();

                     $formattedClassrooms[] = [
                         'classroom_id' => $classroom->classroom_id,
                         'classroom_name' => $classroom->classroom_name,
                         'total_students' => $totalStudents,
                         'male_students' => $maleCount,
                         'female_students' => $femaleCount,
                         'academic_year_id' => $classroom->academic_year_id,
                         'branch_id' => $classroom->branch_id,
                         'status' => $classroom->status
                     ];
                 }

                 return response()->json([
                     'success' => true,
                     'data' => $formattedClassrooms,
                     'total_classrooms' => count($formattedClassrooms)
                 ]);

             } catch (\Exception $e) {
                 return response()->json(['error' => 'Failed to retrieve homeroom classrooms'], 500);
             }
         }

         /**
          * Get students in a specific homeroom classroom
          */
         public function getHomeroomStudents($authCode, $classroomId) {
             $device = MobileDevice::where('auth_code', $authCode)->first();

             if (!$device) {
                 return response()->json(['error' => 'Invalid authentication code'], 401);
             }

             // Check if device is for staff
             if ($device->user_type !== 'staff') {
                 return response()->json(['error' => 'Access denied. Staff authentication required'], 403);
             }

             $teacherId = $device->student_id;

             // Get branch ID for mobile API context
             $branchId = $this->ah->mobileCurrentBranch($teacherId);
             if (!$branchId) {
                 return response()->json(['error' => 'Branch information not found for teacher'], 404);
             }

             $academicYearId = $this->ah->branchAcademicYear($branchId);

             try {
                 // Verify teacher owns this classroom
                 $classroom = \App\Models\Classroom::where('classroom_id', $classroomId)
                     ->where('homeroom_teacher_id', $teacherId)
                     ->where('academic_year_id', $academicYearId)
                     ->first();

                 if (!$classroom) {
                     return response()->json(['error' => 'Classroom not found or access denied'], 404);
                 }

                 // Get students in this classroom
                 $students = \App\Models\StudentClassroom::with('student')
                     ->where('classroom_id', $classroomId)
                     ->where('academic_year_id', $academicYearId)
                     ->get();

                 $formattedStudents = [];
                 foreach ($students as $studentClassroom) {
                     $student = $studentClassroom->student;
                     if ($student) {
                         $formattedStudents[] = [
                             'student_id' => $student->id,
                             'name' => $student->name,
                             'photo' => $student->photo ? "https://sis.bfi.edu.mm" . $student->photo : null,
                             'birth_date' => $student->birth_date,
                             'gender' => $student->gender,
                             'nationality' => $student->nationality,
                             'phone' => $student->phone,
                             'email' => $student->email,
                             'address' => $student->address,
                             'parent_name' => $student->parent_name,
                             'parent_phone' => $student->parent_phone,
                             'emergency_contact' => $student->emergency_contact,
                             'medical_conditions' => $student->medical_conditions,
                             'enrollment_date' => $student->created_at ? $student->created_at->format('Y-m-d') : null
                         ];
                     }
                 }

                 // Sort students by name
                 usort($formattedStudents, function($a, $b) {
                     return strcmp($a['name'], $b['name']);
                 });

                 return response()->json([
                     'success' => true,
                     'data' => [
                         'classroom' => [
                             'classroom_id' => $classroom->classroom_id,
                             'classroom_name' => $classroom->classroom_name,
                             'total_students' => count($formattedStudents)
                         ],
                         'students' => $formattedStudents
                     ]
                 ]);

             } catch (\Exception $e) {
                 return response()->json(['error' => 'Failed to retrieve homeroom students'], 500);
             }
         }

         /**
          * Get homeroom attendance summary for a specific date
          */
         public function getHomeroomAttendance($authCode, $classroomId, $date = null) {
             $device = MobileDevice::where('auth_code', $authCode)->first();

             if (!$device) {
                 return response()->json(['error' => 'Invalid authentication code'], 401);
             }

             // Check if device is for staff
             if ($device->user_type !== 'staff') {
                 return response()->json(['error' => 'Access denied. Staff authentication required'], 403);
             }

             $teacherId = $device->student_id;

             // Get branch ID for mobile API context
             $branchId = $this->ah->mobileCurrentBranch($teacherId);
             if (!$branchId) {
                 return response()->json(['error' => 'Branch information not found for teacher'], 404);
             }

             $academicYearId = $this->ah->branchAcademicYear($branchId);
             $targetDate = $date ?? date('Y-m-d');

             try {
                 // Verify teacher owns this classroom
                 $classroom = \App\Models\Classroom::where('classroom_id', $classroomId)
                     ->where('homeroom_teacher_id', $teacherId)
                     ->where('academic_year_id', $academicYearId)
                     ->first();

                 if (!$classroom) {
                     return response()->json(['error' => 'Classroom not found or access denied'], 404);
                 }

                 // Get students in this classroom
                 $students = \App\Models\StudentClassroom::with('student')
                     ->where('classroom_id', $classroomId)
                     ->where('academic_year_id', $academicYearId)
                     ->get();

                 // Get student IDs for debugging
                 $studentIds = $students->map(function($s) { return $s->student ? $s->student->id : null; })->filter();

                 // First, check if there are any attendance records for these students on this date (without academic year filter)
                 $allAttendanceForDate = \DB::table('student_attendance')
                     ->where('date', $targetDate)
                     ->whereIn('student_id', $studentIds)
                     ->get();  

                 // Get attendance data for the date with academic year filter
                 $attendanceData = \DB::table('student_attendance')
                     ->where('date', $targetDate)
                     ->where('academic_year_id', $academicYearId)
                     ->whereIn('student_id', $studentIds)
                     ->get()
                     ->keyBy('student_id');



                 // If no attendance found with academic year filter but there are records without it,
                 // use the unfiltered data (this handles cases where academic_year_id might be null or different)
                 if ($attendanceData->isEmpty() && $allAttendanceForDate->isNotEmpty()) {
                     $attendanceData = $allAttendanceForDate->keyBy('student_id');
                 }

                 $attendanceSummary = [];
                 $presentCount = 0;
                 $absentCount = 0;
                 $lateCount = 0;

                 foreach ($students as $studentClassroom) {
                     $student = $studentClassroom->student;
                     if ($student) {
                         $attendance = $attendanceData->get($student->id);
                         $status = $attendance ? $attendance->attendance_status : 'not_marked';

                         if ($status === 'present') $presentCount++;
                         elseif ($status === 'absent') $absentCount++;
                         elseif ($status === 'late') $lateCount++;

                         $attendanceSummary[] = [
                             'student_id' => $student->id,
                             'name' => $student->name,
                             'photo' => $student->photo ? "https://sis.bfi.edu.mm" . $student->photo : null,
                             'attendance_status' => $status,
                             'swipe_time' => $attendance->swipe_time ?? null,
                             'notes' => $attendance->notes ?? null
                         ];
                     }
                 }

                 // Sort by attendance status (absent first, then late, then present)
                 usort($attendanceSummary, function($a, $b) {
                     $statusOrder = ['absent' => 1, 'late' => 2, 'present' => 3, 'not_marked' => 4];
                     $aOrder = $statusOrder[$a['attendance_status']] ?? 5;
                     $bOrder = $statusOrder[$b['attendance_status']] ?? 5;

                     if ($aOrder === $bOrder) {
                         return strcmp($a['name'], $b['name']);
                     }
                     return $aOrder - $bOrder;
                 });

                 return response()->json([
                     'success' => true,
                     'data' => [
                         'classroom' => [
                             'classroom_id' => $classroom->classroom_id,
                             'classroom_name' => $classroom->classroom_name
                         ],
                         'date' => $targetDate,
                         'summary' => [
                             'total_students' => count($students),
                             'present' => $presentCount,
                             'absent' => $absentCount,
                             'late' => $lateCount,
                             'not_marked' => count($students) - ($presentCount + $absentCount + $lateCount),
                             'attendance_rate' => count($students) > 0 ? round(($presentCount / count($students)) * 100, 1) : 0
                         ],
                         'students' => $attendanceSummary,
                         'debug_info' => [
                             'academic_year_id' => $academicYearId,
                             'total_attendance_records_found' => $attendanceData->count(),
                             'all_attendance_records_for_date' => $allAttendanceForDate->count()
                         ]
                     ]
                 ]);

             } catch (\Exception $e) {
                 return response()->json(['error' => 'Failed to retrieve homeroom attendance'], 500);
             }
         }

         /**
          * Get homeroom discipline records summary
          */
         public function getHomeroomDiscipline($authCode, $classroomId, $startDate = null, $endDate = null) {
             $device = MobileDevice::where('auth_code', $authCode)->first();

             if (!$device) {
                 return response()->json(['error' => 'Invalid authentication code'], 401);
             }

             // Check if device is for staff
             if ($device->user_type !== 'staff') {
                 return response()->json(['error' => 'Access denied. Staff authentication required'], 403);
             }

             $teacherId = $device->student_id;

             // Get branch ID for mobile API context
             $branchId = $this->ah->mobileCurrentBranch($teacherId);
             if (!$branchId) {
                 return response()->json(['error' => 'Branch information not found for teacher'], 404);
             }

             $academicYearId = $this->ah->branchAcademicYear($branchId);
             $startDate = $startDate ?? date('Y-m-d', strtotime('-30 days'));
             $endDate = $endDate ?? date('Y-m-d');

             try {
                 // Verify teacher owns this classroom
                 $classroom = \App\Models\Classroom::where('classroom_id', $classroomId)
                     ->where('homeroom_teacher_id', $teacherId)
                     ->where('academic_year_id', $academicYearId)
                     ->first();

                 if (!$classroom) {
                     return response()->json(['error' => 'Classroom not found or access denied'], 404);
                 }

                 // Get students in this classroom
                 $students = \App\Models\StudentClassroom::with('student')
                     ->where('classroom_id', $classroomId)
                     ->where('academic_year_id', $academicYearId)
                     ->get();

                 $studentIds = $students->map(function($s) { return $s->student ? $s->student->id : null; })->filter();

                 // Get discipline records for these students
                 $disciplineRecords = \DB::table('discipline_bps_records')
                     ->leftJoin('discipline_bps_items', 'discipline_bps_items.discipline_item_id', 'discipline_bps_records.item_id')
                     ->leftJoin('users as students', 'students.id', 'discipline_bps_records.student_id')
                     ->leftJoin('users as teachers', 'teachers.id', 'discipline_bps_records.user_id')
                     ->select([
                         'discipline_bps_records.*',
                         'discipline_bps_items.item_title',
                         'discipline_bps_items.item_point',
                         'discipline_bps_items.item_type',
                         'students.name as student_name',
                         'students.photo as student_photo',
                         'teachers.name as teacher_name'
                     ])
                     ->where('discipline_bps_records.academic_year_id', $academicYearId)
                     ->whereIn('discipline_bps_records.student_id', $studentIds)
                     ->whereBetween('discipline_bps_records.date', [$startDate, $endDate])
                     ->orderBy('discipline_bps_records.date', 'desc')
                     ->get();

                 // Group records by student
                 $studentDiscipline = [];
                 $totalDpsPoints = 0;
                 $totalPrsPoints = 0;

                 foreach ($students as $studentClassroom) {
                     $student = $studentClassroom->student;
                     if ($student) {
                         $studentRecords = $disciplineRecords->where('student_id', $student->id);
                         $dpsPoints = $studentRecords->where('item_type', 'dps')->sum('item_point');
                         $prsPoints = $studentRecords->where('item_type', 'prs')->sum('item_point');

                         $totalDpsPoints += $dpsPoints;
                         $totalPrsPoints += $prsPoints;

                         $studentDiscipline[] = [
                             'student_id' => $student->id,
                             'name' => $student->name,
                             'photo' => $student->photo ? "https://sis.bfi.edu.mm" . $student->photo : null,
                             'dps_points' => $dpsPoints,
                             'prs_points' => $prsPoints,
                             'total_records' => $studentRecords->count(),
                             'all_records' => $studentRecords->map(function($record) {
                                 return [
                                     'date' => $record->date,
                                     'item_title' => $record->item_title,
                                     'item_point' => $record->item_point,
                                     'item_type' => $record->item_type,
                                     'note' => $record->note,
                                     'teacher_name' => $record->teacher_name
                                 ];
                             })->values()->toArray()
                         ];
                     }
                 }

                 // Sort by total DPS points (highest first)
                 usort($studentDiscipline, function($a, $b) {
                     return $b['dps_points'] - $a['dps_points'];
                 });

                 return response()->json([
                     'success' => true,
                     'data' => [
                         'classroom' => [
                             'classroom_id' => $classroom->classroom_id,
                             'classroom_name' => $classroom->classroom_name
                         ],
                         'date_range' => [
                             'start_date' => $startDate,
                             'end_date' => $endDate
                         ],
                         'summary' => [
                             'total_students' => count($students),
                             'total_dps_points' => $totalDpsPoints,
                             'total_prs_points' => $totalPrsPoints,
                             'total_records' => $disciplineRecords->count(),
                             'students_with_records' => count(array_filter($studentDiscipline, fn($s) => $s['total_records'] > 0))
                         ],
                         'students' => $studentDiscipline
                     ]
                 ]);

             } catch (\Exception $e) {
                 return response()->json(['error' => 'Failed to retrieve homeroom discipline records'], 500);
             }
         }

         /**
          * Get detailed student profile for homeroom teacher
          */
         public function getHomeroomStudentProfile($authCode, $studentId) {
             $device = MobileDevice::where('auth_code', $authCode)->first();

             if (!$device) {
                 return response()->json(['error' => 'Invalid authentication code'], 401);
             }

             // Check if device is for staff
             if ($device->user_type !== 'staff') {
                 return response()->json(['error' => 'Access denied. Staff authentication required'], 403);
             }

             $teacherId = $device->student_id;

             // Get branch ID for mobile API context
             $branchId = $this->ah->mobileCurrentBranch($teacherId);
             if (!$branchId) {
                 return response()->json(['error' => 'Branch information not found for teacher'], 404);
             }

             $academicYearId = $this->ah->branchAcademicYear($branchId);

             try {
                 // Verify student is in teacher's homeroom class
                 $studentClassroom = \App\Models\StudentClassroom::with(['student', 'classroom'])
                     ->where('student_id', $studentId)
                     ->where('academic_year_id', $academicYearId)
                     ->whereHas('classroom', function($query) use ($teacherId) {
                         $query->where('homeroom_teacher_id', $teacherId);
                     })
                     ->first();

                 if (!$studentClassroom) {
                     return response()->json(['error' => 'Student not found in your homeroom class'], 404);
                 }

                 $student = $studentClassroom->student;
                 $classroom = $studentClassroom->classroom;

                 // Get recent attendance (last 30 days) - try with academic year filter first
                 $recentAttendance = \DB::table('student_attendance')
                     ->where('student_id', $studentId)
                     ->where('academic_year_id', $academicYearId)
                     ->where('date', '>=', date('Y-m-d', strtotime('-30 days')))
                     ->orderBy('date', 'desc')
                     ->limit(20) // Get more records to show recent activity
                     ->get();

                 // If no records found with academic year filter, try without it
                 if ($recentAttendance->isEmpty()) {
                     $recentAttendance = \DB::table('student_attendance')
                         ->where('student_id', $studentId)
                         ->where('date', '>=', date('Y-m-d', strtotime('-30 days')))
                         ->orderBy('date', 'desc')
                         ->limit(20)
                         ->get();
                 }

                 // Calculate attendance stats based on actual records only
                 $attendanceStats = [
                     'present' => $recentAttendance->where('attendance_status', 'present')->count(),
                     'absent' => $recentAttendance->where('attendance_status', 'absent')->count(),
                     'late' => $recentAttendance->where('attendance_status', 'late')->count(),
                     'total_days' => $recentAttendance->count()
                 ];

                 // Get school days count for better context (last 30 days, excluding weekends)
                 $startDate = date('Y-m-d', strtotime('-30 days'));
                 $endDate = date('Y-m-d');
                 $schoolDaysCount = 0;
                 $currentDate = new \DateTime($startDate);
                 $endDateObj = new \DateTime($endDate);

                 while ($currentDate <= $endDateObj) {
                     $dayOfWeek = $currentDate->format('N'); // 1 (Monday) to 7 (Sunday)
                     if ($dayOfWeek <= 5) { // Monday to Friday
                         $schoolDaysCount++;
                     }
                     $currentDate = $currentDate->add(new \DateInterval('P1D'));
                 }

                 // Get recent discipline records (last 60 days)
                 $disciplineRecords = \DB::table('discipline_bps_records')
                     ->leftJoin('discipline_bps_items', 'discipline_bps_items.discipline_item_id', 'discipline_bps_records.item_id')
                     ->leftJoin('users', 'users.id', 'discipline_bps_records.user_id')
                     ->select([
                         'discipline_bps_records.date',
                         'discipline_bps_records.note',
                         'discipline_bps_items.item_title',
                         'discipline_bps_items.item_point',
                         'discipline_bps_items.item_type',
                         'users.name as teacher_name'
                     ])
                     ->where('discipline_bps_records.student_id', $studentId)
                     ->where('discipline_bps_records.academic_year_id', $academicYearId)
                     ->where('discipline_bps_records.date', '>=', date('Y-m-d', strtotime('-60 days')))
                     ->orderBy('discipline_bps_records.date', 'desc')
                     ->limit(10)
                     ->get();

                 // Get recent assessments
                 $summativeAssessments = \DB::table('academic_summative_assessments_data')
                     ->leftJoin('academic_summative_assessments', 'academic_summative_assessments.assessment_id', 'academic_summative_assessments_data.assessment_id')
                     ->leftJoin('subjects', 'subjects.subject_id', 'academic_summative_assessments.subject_id')
                     ->select([
                         'academic_summative_assessments.assessment_name',
                         'academic_summative_assessments_data.score',
                         'academic_summative_assessments.max_score',
                         'academic_summative_assessments.date',
                         'subjects.subject_name'
                     ])
                     ->where('academic_summative_assessments_data.student_id', $studentId)
                     ->where('academic_summative_assessments.academic_year_id', $academicYearId)
                     ->orderBy('academic_summative_assessments.date', 'desc')
                     ->limit(5)
                     ->get();

                 // Get library records
                 $libraryRecords = \DB::table('library_issues')
                     ->leftJoin('library_books', 'library_books.book_id', 'library_issues.book_id')
                     ->select([
                         'library_books.title',
                         'library_issues.issue_date',
                         'library_issues.should_return_date',
                         'library_issues.return_date',
                         'library_issues.is_returned'
                     ])
                     ->where('library_issues.user_id', $studentId)
                     ->orderBy('library_issues.issue_date', 'desc')
                     ->limit(5)
                     ->get();

                 // Get parent information from students_family table
                 $fatherInfo = \App\Models\StudentFamily::where('student_id', $studentId)
                     ->where('family_type', 'father')
                     ->first();

                 $motherInfo = \App\Models\StudentFamily::where('student_id', $studentId)
                     ->where('family_type', 'mother')
                     ->first();

                 return response()->json([
                     'success' => true,
                     'data' => [
                         'student' => [
                             'student_id' => $student->id,
                             'name' => $student->name,
                             'photo' => $student->photo ? "https://sis.bfi.edu.mm" . $student->photo : null,
                             'birth_date' => $student->birth_date,
                             'gender' => $student->gender,
                             'nationality' => $student->nationality,
                             'phone' => $student->phone,
                             'email' => $student->email,
                             'address' => $student->address,
                             'parent_name' => $student->parent_name,
                             'parent_phone' => $student->parent_phone,
                             'emergency_contact' => $student->emergency_contact,
                             'medical_conditions' => $student->medical_conditions
                         ],
                         'parents' => [
                             'father' => $fatherInfo ? [
                                 'name' => $fatherInfo->name,
                                 'nationality' => $fatherInfo->nationality,
                                 'passport_number' => $fatherInfo->passport_number,
                                 'passport_country' => $fatherInfo->passport_country,
                                 'passport_expiry_date' => $fatherInfo->passport_expiry_date,
                                 'employer' => $fatherInfo->employer,
                                 'position' => $fatherInfo->position,
                                 'business_address' => $fatherInfo->business_address,
                                 'business_phone' => $fatherInfo->business_phone,
                                 'business_email' => $fatherInfo->business_email,
                                 'email' => $fatherInfo->email,
                                 'mobile' => $fatherInfo->mobile,
                                 'address' => $fatherInfo->address
                             ] : null,
                             'mother' => $motherInfo ? [
                                 'name' => $motherInfo->name,
                                 'nationality' => $motherInfo->nationality,
                                 'passport_number' => $motherInfo->passport_number,
                                 'passport_country' => $motherInfo->passport_country,
                                 'passport_expiry_date' => $motherInfo->passport_expiry_date,
                                 'employer' => $motherInfo->employer,
                                 'position' => $motherInfo->position,
                                 'business_address' => $motherInfo->business_address,
                                 'business_phone' => $motherInfo->business_phone,
                                 'business_email' => $motherInfo->business_email,
                                 'email' => $motherInfo->email,
                                 'mobile' => $motherInfo->mobile,
                                 'address' => $motherInfo->address
                             ] : null
                         ],
                         'classroom' => [
                             'classroom_id' => $classroom->classroom_id,
                             'classroom_name' => $classroom->classroom_name
                         ],
                         'attendance' => [
                             'stats' => $attendanceStats,
                             'attendance_rate' => $attendanceStats['total_days'] > 0 ?
                                 round(($attendanceStats['present'] / $attendanceStats['total_days']) * 100, 1) : 0,
                             'school_days_in_period' => $schoolDaysCount,
                             'days_with_records' => $attendanceStats['total_days'],
                             'days_without_records' => max(0, $schoolDaysCount - $attendanceStats['total_days']),
                             'recent_records' => $recentAttendance->map(function($record) {
                                 return [
                                     'date' => $record->date,
                                     'status' => $record->attendance_status,
                                     'swipe_time' => $record->swipe_time,
                                     'notes' => $record->notes
                                 ];
                             })->toArray()
                         ],
                         'discipline' => [
                             'total_records' => $disciplineRecords->count(),
                             'dps_points' => $disciplineRecords->where('item_type', 'dps')->sum('item_point'),
                             'prs_points' => $disciplineRecords->where('item_type', 'prs')->sum('item_point'),
                             'recent_records' => $disciplineRecords->map(function($record) {
                                 return [
                                     'date' => $record->date,
                                     'item_title' => $record->item_title,
                                     'item_point' => $record->item_point,
                                     'item_type' => $record->item_type,
                                     'note' => $record->note,
                                     'teacher_name' => $record->teacher_name
                                 ];
                             })->toArray()
                         ],
                         'assessments' => [
                             'recent_summative' => $summativeAssessments->map(function($assessment) {
                                 return [
                                     'assessment_title' => $assessment->assessment_name,
                                     'subject_name' => $assessment->subject_name,
                                     'score' => $assessment->score,
                                     'total_marks' => $assessment->max_score,
                                     'percentage' => $assessment->max_score > 0 ?
                                         round(($assessment->score / $assessment->max_score) * 100, 1) : 0,
                                     'date' => $assessment->date
                                 ];
                             })->toArray()
                         ],
                         'library' => [
                             'total_borrowed' => $libraryRecords->count(),
                             'currently_borrowed' => $libraryRecords->where('is_returned', 0)->count(),
                             'recent_books' => $libraryRecords->map(function($record) {
                                 return [
                                     'title' => $record->title,
                                     'issue_date' => $record->issue_date,
                                     'should_return_date' => $record->should_return_date,
                                     'return_date' => $record->return_date,
                                     'is_returned' => $record->is_returned == 1,
                                     'is_overdue' => !$record->is_returned && $record->should_return_date < date('Y-m-d')
                                 ];
                             })->toArray()
                         ]
                     ]
                 ]);

             } catch (\Exception $e) {
                 return response()->json(['error' => 'Failed to retrieve student profile'], 500);
             }
         }

     public function getStudentGrades($authCode) {
             $device = MobileDevice::where('auth_code', $authCode)->first();

             if (!$device) {
                 return response()->json(['error' => 'Invalid authentication code'], 401);
             }

             $branch = StudentInformation::where("id", $device->student_id)->first();

             if (!$branch) {
                 return response()->json(['error' => 'Student information not found'], 404);
             }

             $userId = $device->student_id;
             $academicYearId = $this->ah->branchAcademicYear($branch->branch_id);

             if (!$academicYearId) {
                 return response()->json(['error' => 'Academic year not found for this branch'], 404);
             }

             // Get summative assessments - include both graded and ungraded assessments
             // First get all assessments for this student's classes, then left join with student data
             $summativeList = \DB::table('academic_summative_assessments')
                                 ->leftJoin('academic_summative_assessments_data', function($join) use ($userId) {
                                     $join->on('academic_summative_assessments.assessment_id', '=', 'academic_summative_assessments_data.assessment_id')
                                          ->where('academic_summative_assessments_data.student_id', '=', $userId);
                                 })
                                 ->leftJoin('subjects', 'subjects.subject_id', 'academic_summative_assessments.subject_id')
                                 ->leftJoin('users', 'users.id', 'academic_summative_assessments.teacher_id')
                                 ->leftJoin('skills_strands', 'skills_strands.skill_strand_id', 'academic_summative_assessments.strand')
                                 ->leftJoin('academic_elective_grade_students', function($join) use ($userId) {
                                     $join->on('academic_elective_grade_students.grade_id', '=', 'academic_summative_assessments.grade_id')
                                          ->where('academic_elective_grade_students.student_id', '=', $userId);
                                 })
                                 ->where('academic_summative_assessments.academic_year_id', $academicYearId)
                                 ->whereNotNull('academic_elective_grade_students.student_id') // Only assessments for student's classes
                                 ->orderBy('academic_summative_assessments.created_at', 'desc')
                                 ->select(
                                     'academic_summative_assessments.assessment_id',
                                     'academic_summative_assessments.date',
                                     'subjects.subject_name',
                                     'academic_summative_assessments.assessment_name',
                                     'academic_summative_assessments_data.type_title',
                                     'academic_summative_assessments_data.score',
                                     'academic_summative_assessments_data.score_percentage',
                                     'academic_summative_assessments.max_score',
                                     'academic_summative_assessments.strand as strand_id',
                                     'skills_strands.value as strand_name',
                                     'users.name as teacher_name',
                                     \DB::raw('CASE WHEN academic_summative_assessments_data.student_id IS NOT NULL THEN 1 ELSE 0 END as is_graded'),
                                     \DB::raw('CASE WHEN academic_summative_assessments_data.score IS NULL THEN "Not Graded" ELSE CAST(academic_summative_assessments_data.score as CHAR) END as display_score')
                                 )
                                 ->get();

             // Get formative assessments - include both graded and ungraded assessments
             // First get all assessments for this student's classes, then left join with student data
             $formativeList = \DB::table('academic_formative_assessments')
                                 ->leftJoin('academic_formative_assessments_data', function($join) use ($userId) {
                                     $join->on('academic_formative_assessments.formative_assessment_id', '=', 'academic_formative_assessments_data.formative_assessment_id')
                                          ->where('academic_formative_assessments_data.student_id', '=', $userId);
                                 })
                                 ->leftJoin('subjects', 'subjects.subject_id', 'academic_formative_assessments.subject_id')
                                 ->leftJoin('users', 'users.id', 'academic_formative_assessments.teacher_id')
                                 ->leftJoin('skills_strands as strand_info', 'strand_info.skill_strand_id', 'academic_formative_assessments.strand')
                                 ->leftJoin('skills_strands as skill_info', 'skill_info.skill_strand_id', 'academic_formative_assessments.skill')
                                 ->leftJoin('academic_elective_grade_students', function($join) use ($userId) {
                                     $join->on('academic_elective_grade_students.grade_id', '=', 'academic_formative_assessments.grade_id')
                                          ->where('academic_elective_grade_students.student_id', '=', $userId);
                                 })
                                 ->where('academic_formative_assessments.academic_year_id', $academicYearId)
                                 ->whereNotNull('academic_elective_grade_students.student_id') // Only assessments for student's classes
                                 ->orderBy('academic_formative_assessments.created_at', 'desc')
                                 ->select([
                                     'academic_formative_assessments.formative_assessment_id as assessment_id',
                                     'academic_formative_assessments_data.t1 as tt1',
                                     'academic_formative_assessments_data.t2 as tt2',
                                     'academic_formative_assessments_data.t3 as tt3',
                                     'academic_formative_assessments_data.t4 as tt4',
                                     'users.name as teacher_name',
                                     'subjects.subject_name',
                                     'academic_formative_assessments.assessment_name',
                                     'academic_formative_assessments.strand as strand_id',
                                     'strand_info.value as strand_name',
                                     'academic_formative_assessments.skill as skill_id',
                                     'skill_info.value as skill_name',
                                     'academic_formative_assessments.date',
                                     \DB::raw('NULL as max_score'),
                                     \DB::raw('CASE WHEN academic_formative_assessments_data.student_id IS NOT NULL THEN 1 ELSE 0 END as is_graded'),
                                     \DB::raw('CASE WHEN academic_formative_assessments_data.t1 IS NULL AND academic_formative_assessments_data.t2 IS NULL AND academic_formative_assessments_data.t3 IS NULL AND academic_formative_assessments_data.t4 IS NULL THEN "Not Graded" ELSE "Graded" END as grading_status')
                                 ])
                                 ->get();

             // Add summary statistics
             $summativeStats = [
                 'total_assessments' => $summativeList->count(),
                 'graded_assessments' => $summativeList->where('is_graded', 1)->count(),
                 'ungraded_assessments' => $summativeList->where('is_graded', 0)->count()
             ];

             $formativeStats = [
                 'total_assessments' => $formativeList->count(),
                 'graded_assessments' => $formativeList->where('is_graded', 1)->count(),
                 'ungraded_assessments' => $formativeList->where('is_graded', 0)->count()
             ];

             return response()->json([
                 'success' => true,
                 'student_id' => $userId,
                 'academic_year_id' => $academicYearId,
                 'summative' => $summativeList,
                 'formative' => $formativeList,
                 'statistics' => [
                     'summative' => $summativeStats,
                     'formative' => $formativeStats,
                     'total_assessments' => $summativeStats['total_assessments'] + $formativeStats['total_assessments'],
                     'total_graded' => $summativeStats['graded_assessments'] + $formativeStats['graded_assessments'],
                     'total_ungraded' => $summativeStats['ungraded_assessments'] + $formativeStats['ungraded_assessments']
                 ]
             ]);
         }
     public function getTeacherTimetableData($authCode) {
             $device = MobileDevice::where('auth_code', $authCode)->first();

             if (!$device) {
                 return response()->json(['error' => 'Invalid authentication code'], 401);
             }

             // Check if device is for staff
             if ($device->user_type !== 'staff') {
                 return response()->json(['error' => 'Access denied. Staff authentication required'], 403);
             }

             $userId = $device->student_id; // This is actually staff ID for teacher devices
             $branches = \DB::table('users_branches')
                 ->leftJoin('branches', 'branches.branch_id', 'users_branches.branch_id')
                 ->where('user_id', $userId)
                 ->get();

             if ($branches->isEmpty()) {
                 return response()->json(['error' => 'No branches found for teacher'], 404);
             }

             $today = date('Y-m-d');
             $branchesData = [];

             // Get global academic year info (active academic year)
             $globalAcademicYear = AcademicYear::where('is_active', 1)->first();
             $globalAcademicYearInfo = null;
             if ($globalAcademicYear) {
                 $globalAcademicYearInfo = [
                     'academic_year_id' => $globalAcademicYear->academic_year_id,
                     'academic_year' => $globalAcademicYear->academic_year,
                     'start_date' => $globalAcademicYear->start_date,
                     'end_date' => $globalAcademicYear->end_date,
                     'is_active' => $globalAcademicYear->is_active
                 ];
             }

             foreach ($branches as $branch) {
                 $branchId = $branch->branch_id;
                 $academicYearId = $this->ah->branchAcademicYear($branchId);

                 if (!$academicYearId) {
                     continue; // Skip branches without academic year
                 }

                 // Get academic year details
                 $academicYear = AcademicYear::where('academic_year_id', $academicYearId)->first();
                 $academicYearInfo = null;
                 if ($academicYear) {
                     $academicYearInfo = [
                         'academic_year_id' => $academicYear->academic_year_id,
                         'academic_year' => $academicYear->academic_year,
                         'start_date' => $academicYear->start_date,
                         'end_date' => $academicYear->end_date,
                         'is_active' => $academicYear->is_active
                     ];
                 }

                 // Get current week info
                 $weekInfo = \DB::table('academic_week')
                     ->where('start_date', '<=', $today)
                     ->where('end_date', '>=', $today)
                     ->where('branch_id', $branchId)
                     ->first();

                 $currentWeek = $weekInfo ? $weekInfo->week : null;

                 // Get timetable with attendance status
                 $timetableQuery = \DB::table('academic_timetable')
                     ->selectRaw('
                         academic_timetable.timetable_id,
                         academic_timetable.week_day,
                         academic_timetable.week_time,
                         academic_timetable.grade_id,
                         academic_timetable.subject_id,
                         subjects.subject_name,
                         academic_elective_grade.grade_name,
                         count(students_attendance_class.class_attendance_id) as attendance_count
                     ')
                     ->leftJoin('subjects', 'subjects.subject_id', 'academic_timetable.subject_id')
                     ->leftJoin('academic_elective_grade', 'academic_elective_grade.grade_id', 'academic_timetable.grade_id')
                     ->leftJoin('students_attendance_class', function($join) use($academicYearId, $currentWeek) {
                         $join->where('students_attendance_class.academic_year_id', $academicYearId);
                         $join->on('students_attendance_class.week_day', 'academic_timetable.week_day');
                         $join->on('students_attendance_class.week_time', 'academic_timetable.week_time');
                         $join->on('students_attendance_class.grade_id', 'academic_timetable.grade_id');
                         $join->on('students_attendance_class.subject_id', 'academic_timetable.subject_id');
                         if ($currentWeek) {
                             $join->where('students_attendance_class.week', $currentWeek);
                         }
                     })
                     ->where('academic_timetable.academic_year_id', $academicYearId)
                     ->where('academic_timetable.branch_id', $branchId)
                     ->where('academic_timetable.user_id', $userId)
                     ->groupBy([
                         'academic_timetable.timetable_id',
                         'academic_timetable.week_day',
                         'academic_timetable.week_time',
                         'academic_timetable.grade_id',
                         'academic_timetable.subject_id',
                         'subjects.subject_name',
                         'academic_elective_grade.grade_name'
                     ])
                     ->orderBy('week_day', 'ASC')
                     ->orderBy('week_time', 'ASC')
                     ->get();

                 $formattedTimetable = [];
                 foreach ($timetableQuery as $item) {
                     $formattedTimetable[] = [
                         'timetable_id' => $item->timetable_id,
                         'week_day' => $item->week_day,
                         'week_time' => $item->week_time,
                         'grade_id' => $item->grade_id,
                         'subject_id' => $item->subject_id,
                         'subject_name' => $item->subject_name ?? 'Unknown Subject',
                         'grade_name' => $item->grade_name ?? 'Unknown Grade',
                         'attendance_taken' => $item->attendance_count > 0,
                         'attendance_count' => $item->attendance_count,
                         'current_week' => $currentWeek
                     ];
                 }

                 $branchesData[] = [
                     'branch_id' => $branchId,
                     'branch_name' => $branch->branch_name,
                     'branch_description' => $branch->branch_description ?? null,
                     'academic_year_id' => $academicYearId,
                     'academic_year_info' => $academicYearInfo,
                     'current_week' => $currentWeek,
                     'timetable' => $formattedTimetable
                 ];
             }

             return response()->json([
                 'success' => true,
                 'teacher_id' => $userId,
                 'global_academic_year' => $globalAcademicYearInfo,
                 'branches' => $branchesData,
                 'total_branches' => count($branchesData)
             ]);
         }

         public function getTeacherBpsData($authCode) {
             $device = MobileDevice::where('auth_code', $authCode)->first();

             if (!$device) {
                 return response()->json(['error' => 'Invalid authentication code'], 401);
             }

             // Check if device is for staff
             if ($device->user_type !== 'staff') {
                 return response()->json(['error' => 'Access denied. Staff authentication required'], 403);
             }

             $userId = $device->student_id; // This is actually staff ID for teacher devices
             $branches = \DB::table('users_branches')
                 ->leftJoin('branches', 'branches.branch_id', 'users_branches.branch_id')
                 ->where('user_id', $userId)
                 ->get();

             if ($branches->isEmpty()) {
                 return response()->json(['error' => 'No branches found for teacher'], 404);
             }

             // Get discipline items
             $dpsItems = \DB::table('discipline_bps_items')
                 ->where('item_status', 1)
                 ->where('item_type', 'dps')
                 ->select('discipline_item_id', 'item_title', 'item_point', 'item_type')
                 ->get();

             $prsItems = \DB::table('discipline_bps_items')
                 ->where('item_status', 1)
                 ->where('item_type', 'prs')
                 ->select('discipline_item_id', 'item_title', 'item_point', 'item_type')
                 ->get();

             // Include CSRF token in the response
             $csrfToken = csrf_token();

             $branchesData = [];

             // Get global academic year info (active academic year)
             $globalAcademicYear = AcademicYear::where('is_active', 1)->first();
             $globalAcademicYearInfo = null;
             if ($globalAcademicYear) {
                 $globalAcademicYearInfo = [
                     'academic_year_id' => $globalAcademicYear->academic_year_id,
                     'academic_year' => $globalAcademicYear->academic_year,
                     'start_date' => $globalAcademicYear->start_date,
                     'end_date' => $globalAcademicYear->end_date,
                     'is_active' => $globalAcademicYear->is_active
                 ];
             }

             foreach ($branches as $branch) {
                 $branchId = $branch->branch_id;
                 $academicYearId = $this->ah->branchAcademicYear($branchId);

                 if (!$academicYearId) {
                     continue; // Skip branches without academic year
                 }

                 // Get academic year details
                 $academicYear = AcademicYear::where('academic_year_id', $academicYearId)->first();
                 $academicYearInfo = null;
                 if ($academicYear) {
                     $academicYearInfo = [
                         'academic_year_id' => $academicYear->academic_year_id,
                         'academic_year' => $academicYear->academic_year,
                         'start_date' => $academicYear->start_date,
                         'end_date' => $academicYear->end_date,
                         'is_active' => $academicYear->is_active
                     ];
                 }

                 // Get all active classrooms in the branch
                 $allActiveClassrooms = \DB::table('classrooms')
                     ->select([
                         'classrooms.classroom_id',
                         'classrooms.classroom_name',
                         'classrooms.status'
                     ])
                     ->where('classrooms.academic_year_id', $academicYearId)
                     ->where('classrooms.branch_id', $branchId)
                     ->where('classrooms.status', 1) // Only active classrooms
                     ->orderBy('classrooms.classroom_name', 'ASC')
                     ->get();

                 // No complex grouping needed - each classroom is a separate class

                 $classesData = [];
                 foreach ($allActiveClassrooms as $classroom) {
                     // Get students in this classroom
                     $classroomStudents = \DB::table('students_classroom')
                         ->select([
                             'students_classroom.student_id',
                             'users.name as student_name',
                             'users.photo as student_photo'
                         ])
                         ->leftJoin('users', 'users.id', 'students_classroom.student_id')
                         ->where('students_classroom.classroom_id', $classroom->classroom_id)
                         ->where('students_classroom.academic_year_id', $academicYearId)
                         ->where('students_classroom.branch_id', $branchId)
                         ->where('users.user_status', 1)
                         ->orderBy('users.name', 'ASC')
                         ->get();

                     // Format student data
                     $formattedStudents = [];
                     foreach ($classroomStudents as $student) {
                         $formattedStudents[] = [
                             'student_id' => $student->student_id,
                             'student_name' => $student->student_name ?? 'Unknown Student',
                             'student_photo' => $student->student_photo,
                             'classroom_name' => $classroom->classroom_name
                         ];
                     }

                     // Get subjects taught in this classroom (from timetable)
                     $classroomSubjects = \DB::table('academic_timetable')
                         ->select([
                             'academic_timetable.subject_id',
                             'subjects.subject_name',
                             'users.name as teacher_name'
                         ])
                         ->leftJoin('subjects', 'subjects.subject_id', 'academic_timetable.subject_id')
                         ->leftJoin('users', 'users.id', 'academic_timetable.user_id')
                         ->leftJoin('academic_elective_grade', 'academic_elective_grade.grade_id', 'academic_timetable.grade_id')
                         ->leftJoin('academic_elective_grade_students', 'academic_elective_grade_students.grade_id', 'academic_elective_grade.grade_id')
                         ->leftJoin('students_classroom', 'students_classroom.student_id', 'academic_elective_grade_students.student_id')
                         ->where('academic_timetable.academic_year_id', $academicYearId)
                         ->where('academic_timetable.branch_id', $branchId)
                         ->where('students_classroom.classroom_id', $classroom->classroom_id)
                         ->distinct()
                         ->get();

                     $classesData[] = [
                         'classroom_id' => $classroom->classroom_id,
                         'grade_name' => $classroom->classroom_name, // Use classroom name as grade name
                         'grade_status' => $classroom->status,
                         'year_level_group' => false, // No grouping with classroom-based approach
                         'grouped_grade_ids' => [$classroom->classroom_id], // Single classroom ID
                         'subjects' => $classroomSubjects,
                         'students' => $formattedStudents,
                         'total_students' => count($formattedStudents),
                         'total_subjects' => count($classroomSubjects)
                     ];
                 }

                 // Get BPS records created by this teacher
                 $bpsRecords = \DB::table('discipline_bps_records')
                     ->select([
                         'discipline_bps_records.discipline_record_id',
                         'discipline_bps_records.student_id',
                         'discipline_bps_records.item_type',
                         'discipline_bps_records.date',
                         'discipline_bps_records.note',
                         'users.name as student_name',
                         'discipline_bps_items.item_title',
                         'discipline_bps_items.item_point',
                         'classrooms.classroom_name'
                     ])
                     ->leftJoin('users', 'users.id', 'discipline_bps_records.student_id')
                     ->leftJoin('discipline_bps_items', 'discipline_bps_items.discipline_item_id', 'discipline_bps_records.item_id')
                     ->leftJoin('students_classroom', function($join) use($academicYearId, $branchId) {
                         $join->on('students_classroom.student_id', 'discipline_bps_records.student_id')
                             ->where('students_classroom.branch_id', $branchId)
                             ->where('students_classroom.academic_year_id', $academicYearId);
                     })
                     ->leftJoin('classrooms', 'classrooms.classroom_id', 'students_classroom.classroom_id')
                     ->where('discipline_bps_records.status', 1)
                     ->where('discipline_bps_records.academic_year_id', $academicYearId)
                     ->where('discipline_bps_records.branch_id', $branchId)
                     ->where('discipline_bps_records.user_id', $userId)
                     ->where('users.user_status', 1)
                     ->orderBy('discipline_bps_records.discipline_record_id', 'desc')
                     ->get();

                 $branchesData[] = [
                     'branch_id' => $branchId,
                     'branch_name' => $branch->branch_name,
                     'branch_description' => $branch->branch_description ?? null,
                     'academic_year_id' => $academicYearId,
                     'academic_year_info' => $academicYearInfo,
                     'classes' => $classesData,
                     'bps_records' => $bpsRecords,
                     'total_classes' => count($classesData),
                     'total_students' => array_sum(array_column($classesData, 'total_students')),
                     'total_bps_records' => count($bpsRecords)
                 ];
             }

             // Check BPS delete permission for this user
             $hasBpsDeletePermission = false;
             try {
                 // Get the staff user
                 $staffUser = User::find($userId);
                 if ($staffUser && $staffUser->id) {
                     // Check BPS delete permission (module 56, action 2)
                     $module = 56;
                     $action = 2;
                     $colName = "p" . $action;

                     // Get branch ID safely - use the staff user's branch or first available branch
                     $permissionBranchId = null;
                     if ($staffUser->branch_id) {
                         $permissionBranchId = $staffUser->branch_id;
                     } else {
                         // Fallback: get first branch from the branches data we already have
                         if (!empty($branchesData) && isset($branchesData[0]['branch_id'])) {
                             $permissionBranchId = $branchesData[0]['branch_id'];
                         }
                     }

                     if ($permissionBranchId) {
                         // Check permission using the same logic as middleware
                         $hasBpsDeletePermission = \App\Models\Permission::leftJoin('roles_user', 'roles_user.role_id', 'permissions.role_id')
                                                                         ->where('permissions.module_id', $module)
                                                                         ->where('roles_user.user_id', $staffUser->id)
                                                                         ->where('roles_user.branch_id', $permissionBranchId)
                                                                         ->where('permissions.' . $colName, 1)
                                                                         ->count() > 0;
                     }
                 }
             } catch (\Exception $e) {
                 // If permission check fails, default to false for security
                 $hasBpsDeletePermission = false;
             }

             return response()->json([
                 'success' => true,
                 'teacher_id' => $userId,
                 'global_academic_year' => $globalAcademicYearInfo,
                 'branches' => $branchesData,
                 'discipline_items' => [
                     'dps_items' => $dpsItems,
                     'prs_items' => $prsItems
                 ],
                 'permissions' => [
                     'can_delete_bps' => $hasBpsDeletePermission,
                     'permission_details' => [
                         'module_id' => 56,
                         'action_id' => 2,
                         'permission_name' => 'BPS Delete Permission'
                     ]
                 ],
                 'csrf_token' => $csrfToken,
                 'total_branches' => count($branchesData)
             ]);
         }



         public function getAttendanceDetails($authCode, $timetableId) {
                 try {
                     $device = MobileDevice::where('auth_code', $authCode)->first();

                     if (!$device) {
                         return response()->json(['error' => 'Invalid authentication code'], 401);
                     }

                     // Check if device is for staff
                     if ($device->user_type !== 'staff') {
                         return response()->json(['error' => 'Access denied. Staff authentication required'], 403);
                     }

                     $userId = $device->student_id; // This is actually staff ID for teacher devices

                     // Get timetable information
                     $timetable = \DB::table('academic_timetable')
                         ->select([
                             'academic_timetable.*',
                             'subjects.subject_name',
                             'academic_elective_grade.grade_name',
                             'branches.branch_name'
                         ])
                         ->leftJoin('subjects', 'subjects.subject_id', 'academic_timetable.subject_id')
                         ->leftJoin('academic_elective_grade', 'academic_elective_grade.grade_id', 'academic_timetable.grade_id')
                         ->leftJoin('branches', 'branches.branch_id', 'academic_timetable.branch_id')
                         ->where('academic_timetable.timetable_id', $timetableId)
                         ->where('academic_timetable.user_id', $userId)
                         ->first();

                     if (!$timetable) {
                         return response()->json(['error' => 'Timetable not found or access denied'], 404);
                     }

                 $branchId = $timetable->branch_id;
                 $academicYearId = $timetable->academic_year_id;
                 $gradeId = $timetable->grade_id;
                 $subjectId = $timetable->subject_id;
                 $weekDay = $timetable->week_day;
                 $weekTime = $timetable->week_time;

                 // Get current week
                 $today = date('Y-m-d');
                 $weekInfo = \DB::table('academic_week')
                     ->where('start_date', '<=', $today)
                     ->where('end_date', '>=', $today)
                     ->where('branch_id', $branchId)
                     ->where('academic_year_id', $academicYearId)
                     ->first();

                 $currentWeek = $weekInfo ? $weekInfo->week : null;

                 // Calculate the date for the specific weekday
                 $currentDate = new \DateTime();
                 $currentDayOfWeek = $currentDate->format('N'); // 1 (Monday) to 7 (Sunday)
                 $daysToAdd = ($weekDay - $currentDayOfWeek) % 7;
                 if ($daysToAdd < 0) {
                     $daysToAdd += 7;
                 }
                 $selectedDate = clone $currentDate;
                 $selectedDate = $selectedDate->add(new \DateInterval('P' . $daysToAdd . 'D'));
                 $attendanceDate = $selectedDate->format('Y-m-d');

                 // First, get all students for this grade (no duplicates)
                 $students = ElectiveGradeStudent::select([
                         'academic_elective_grade_students.student_id',
                         'users.name as student_name',
                         'users.photo as student_photo'
                     ])
                     ->leftJoin('users', 'users.id', 'academic_elective_grade_students.student_id')
                     ->where('academic_elective_grade_students.grade_id', $gradeId)
                     ->where('users.user_status', 1)
                     ->orderBy('users.name', 'ASC')
                     ->get();

                 // Get attendance data separately to avoid duplicates
                 $attendanceData = \DB::table('students_attendance_class')
                     ->select([
                         'student_id',
                         'attendance_status',
                         'attendance_note'
                     ])
                     ->where('subject_id', $subjectId)
                     ->where('week_day', $weekDay)
                     ->where('week_time', $weekTime)
                     ->where('grade_id', $gradeId);

                 if ($currentWeek) {
                     $attendanceData = $attendanceData->where('week', $currentWeek);
                 }

                 $attendanceData = $attendanceData->get()->keyBy('student_id');

                 // Get classroom information separately for each student
                 $studentClassrooms = \DB::table('students_classroom')
                     ->leftJoin('classrooms', 'classrooms.classroom_id', 'students_classroom.classroom_id')
                     ->where('students_classroom.academic_year_id', $academicYearId)
                     ->where('students_classroom.branch_id', $branchId)
                     ->select('students_classroom.student_id', 'classrooms.classroom_name')
                     ->get()
                     ->keyBy('student_id');

                 // Format student data
                 $formattedStudents = [];
                 foreach ($students as $student) {
                     $attendanceInfo = $attendanceData->get($student->student_id);
                     $attendanceStatus = $attendanceInfo ? $attendanceInfo->attendance_status : 'not_taken';
                     $attendanceNote = $attendanceInfo ? $attendanceInfo->attendance_note : '';
                     $classroomInfo = $studentClassrooms->get($student->student_id);

                     $formattedStudents[] = [
                         'student_id' => $student->student_id,
                         'student_name' => $student->student_name ?? 'Unknown Student',
                         'student_photo' => $student->student_photo,
                         'classroom_name' => $classroomInfo ? $classroomInfo->classroom_name : 'No Classroom',
                         'attendance_status' => $attendanceStatus,
                         'attendance_note' => $attendanceNote,
                         'attendance_options' => [
                             'present' => $attendanceStatus === 'present',
                             'late' => $attendanceStatus === 'late',
                             'absent' => $attendanceStatus === 'absent'
                         ]
                     ];
                 }

                     return response()->json([
                         'success' => true,
                         'timetable_info' => [
                             'timetable_id' => $timetable->timetable_id,
                             'subject_name' => $timetable->subject_name ?? 'Unknown Subject',
                             'grade_name' => $timetable->grade_name ?? 'Unknown Grade',
                             'branch_name' => $timetable->branch_name ?? 'Unknown Branch',
                             'week_day' => $weekDay,
                             'week_time' => $weekTime,
                             'current_week' => $currentWeek,
                             'attendance_date' => $attendanceDate
                         ],
                         'students' => $formattedStudents,
                         'total_students' => count($formattedStudents),
                         'attendance_summary' => [
                             'present_count' => count(array_filter($formattedStudents, function($s) { return $s['attendance_status'] === 'present'; })),
                             'late_count' => count(array_filter($formattedStudents, function($s) { return $s['attendance_status'] === 'late'; })),
                             'absent_count' => count(array_filter($formattedStudents, function($s) { return $s['attendance_status'] === 'absent'; })),
                             'not_taken_count' => count(array_filter($formattedStudents, function($s) { return $s['attendance_status'] === 'not_taken'; }))
                         ]
                     ]);
                 } catch (\Exception $e) {
                     return response()->json([
                         'error' => 'Server error occurred',
                         'message' => $e->getMessage(),
                         'line' => $e->getLine(),
                         'file' => basename($e->getFile())
                     ], 500);
                 }
             }
         /**
          * Store BPS records with comprehensive business logic including point tracking and detention calculation
          *
          * @param array $data - Contains BPS data including students, items, branch, date, etc.
          * @return array - Response with success status and detailed results
          */
         public function storeBps($data) {
             try {
                 // Validate required parameters
                 if (!isset($data['branch_id']) || !isset($data['case_type']) || !isset($data['date']) || !isset($data['user_id'])) {
                     return [
                         'success' => false,
                         'message' => 'Missing required parameters: branch_id, case_type, date, and user_id are required',
                         'error_code' => 'MISSING_PARAMETERS'
                     ];
                 }

                 $branchId = $data['branch_id'];
                 $caseType = $data['case_type']; // '1' for DPS, '0' for PRS
                 $itemType = $caseType == '1' ? 'dps' : 'prs'; // Convert to string format
                 $date = $data['date'];
                 $note = $data['note'] ?? '';
                 $userId = $data['user_id'];
                 $ah = new AcademicHelper();

                 $academicYearId = $ah->branchAcademicYear($branchId);

                 // Get academic semester
                 $academicSemester = $data['academic_semester'] ?? null;
                 if (!$academicSemester) {
                     $semesterInfo = \DB::table('academic_semester')
                         ->where('branch_id', $branchId)
                         ->where('is_default', 1)
                         ->first();

                     $academicSemester = $semesterInfo ? $semesterInfo->academic_semester : $ah->currentSemester();
                 }

                 $results = [];
                 $successCount = 0;
                 $totalRecords = 0;
                 $detentionRecords = [];

                 // Handle both single and multiple students
                 $students = [];
                 if (isset($data['students']) && is_array($data['students'])) {
                     $students = $data['students'];
                 } elseif (isset($data['student_id'])) {
                     $students = [$data['student_id']];
                 } elseif (isset($data['student']) && is_array($data['student'])) {
                     $students = $data['student'];
                 } else {
                     return [
                         'success' => false,
                         'message' => 'No students provided',
                         'error_code' => 'NO_STUDENTS'
                     ];
                 }

                 // Handle item selection based on case type
                 $itemId = null;
                 if ($caseType == '1' && isset($data['dps_case'])) {
                     $itemId = $data['dps_case'];
                 } elseif ($caseType == '0' && isset($data['prs_case'])) {
                     $itemId = $data['prs_case'];
                 } elseif (isset($data['item_id'])) {
                     $itemId = $data['item_id'];
                 } elseif (isset($data['items']) && is_array($data['items']) && count($data['items']) > 0) {
                     $itemId = $data['items'][0]; // Take first item for now
                 } else {
                     return [
                         'success' => false,
                         'message' => 'No BPS item provided for case type: ' . ($itemType === 'dps' ? 'DPS' : 'PRS'),
                         'error_code' => 'NO_ITEM'
                     ];
                 }

                 // Get discipline item details
                 $disciplineItem = DisciplineItem::where('discipline_item_id', $itemId)
                     ->where('item_type', $itemType)
                     ->where('item_status', 1)
                     ->first();

                 if (!$disciplineItem) {
                     return [
                         'success' => false,
                         'message' => 'Invalid BPS item ID or item not found for case type: ' . ($itemType === 'dps' ? 'DPS' : 'PRS'),
                         'error_code' => 'INVALID_ITEM'
                     ];
                 }

                 // Process each student (following web BPS repository pattern)
                 foreach ($students as $studentId) {
                     $totalRecords++;

                     try {
                         // Create discipline record
                         $record = new DisciplineRecord();
                         $record->academic_year_id = $academicYearId;
                         $record->academic_semester = $academicSemester;
                         $record->date = $date;
                         $record->branch_id = $branchId;
                         $record->user_id = $userId;
                         $record->student_id = $studentId;
                         $record->item_type = $itemType;
                         $record->item_id = $itemId;
                         $record->item_title = $disciplineItem->item_title;
                         $record->item_point = $disciplineItem->item_point;
                         $record->status = 1;
                         $record->note = $note;
                         $record->save();

                         // Handle award point tracking (from web BPS repository)
                         $awardPointRecord = DisciplineAwardRemainingPoint::where('student_id', $studentId)
                             ->where('academic_year_id', $academicYearId)
                             ->first();

                         if (!$awardPointRecord) {
                             // Create new award point record if it doesn't exist
                             $newAwardPointRecord = new DisciplineAwardRemainingPoint();
                             $newAwardPointRecord->student_id = $studentId;
                             $newAwardPointRecord->academic_year_id = $academicYearId;
                             $newAwardPointRecord->branch_id = $branchId;
                             $newAwardPointRecord->remaining_point = $disciplineItem->item_point;
                             $newAwardPointRecord->save();
                         } else {
                             // Update existing award point record
                             $newPoint = $awardPointRecord->remaining_point + $disciplineItem->item_point;
                             $awardPointRecord->update(['remaining_point' => $newPoint]);
                         }

                         // Calculate BPS points and detention (only for DPS records)
                         if ($itemType === 'dps') {
                             $detentionInfo = $this->calculateBpsPoint($data, $studentId, [], $record);
                             if ($detentionInfo) {
                                 $detentionRecords[] = $detentionInfo;
                             }
                         }

                         // Send BPS notification
                         $notificationSent = false;
                         $notificationMessage = '';
                         try {
                             $notificationRepository = new MobileNotificationRepository($this->ah);

                             // Get teacher name
                             $teacher = \App\Models\User::find($userId);
                             $teacherName = $teacher ? $teacher->name : 'Unknown';

                             // Get subject information from current timetable context
                             $subjectName = null;
                             $currentTime = date('H:i');
                             $currentDay = strtolower(date('l'));

                             // Try to find current class/subject for the teacher
                             $currentClass = \DB::table('academic_timetable')
                                 ->join('subjects', 'academic_timetable.subject_id', '=', 'subjects.subject_id')
                                 ->join('elective_grades', 'academic_timetable.grade_id', '=', 'elective_grades.grade_id')
                                 ->join('elective_students', 'elective_grades.grade_id', '=', 'elective_students.grade_id')
                                 ->where('elective_students.student_id', $studentId)
                                 ->where('academic_timetable.user_id', $userId)
                                 ->where('academic_timetable.branch_id', $data['branch_id'])
                                 ->where('academic_timetable.' . $currentDay, '!=', '')
                                 ->select('subjects.subject_name')
                                 ->first();

                             if ($currentClass) {
                                 $subjectName = $currentClass->subject_name;
                             }

                             $bpsNotificationData = [
                                 'student_id' => $studentId,
                                 'user_id' => $userId,
                                 'item_type' => $itemType,
                                 'item_title' => $disciplineItem->item_title,
                                 'item_point' => $disciplineItem->item_point,
                                 'date' => $date,
                                 'id' => $record->discipline_record_id,
                                 'branch_id' => $data['branch_id'],
                                 'teacher_name' => $teacherName,
                                 'subject_name' => $subjectName
                             ];
                             $notificationResult = $notificationRepository->sendBpsNotification($bpsNotificationData);
                             $notificationSent = $notificationResult ? true : false;
                             $notificationMessage = $notificationSent ? 'BPS notification sent successfully' : 'Failed to send BPS notification';
                         } catch (\Exception $e) {
                             // Don't fail BPS creation if notification fails
                             $notificationSent = false;
                             $notificationMessage = 'Notification error: ' . $e->getMessage();
                         }

                         $successCount++;
                         $results[] = [
                             'student_id' => $studentId,
                             'item_id' => $itemId,
                             'item_title' => $disciplineItem->item_title,
                             'item_point' => $disciplineItem->item_point,
                             'item_type' => strtoupper($itemType),
                             'status' => 'success',
                             'record_id' => $record->discipline_record_id,
                             'award_point_updated' => true,
                             'notification_sent' => $notificationSent,
                             'notification_message' => $notificationMessage
                         ];

                     } catch (\Exception $e) {
                         $results[] = [
                             'student_id' => $studentId,
                             'item_id' => $itemId,
                             'status' => 'error',
                             'message' => 'Database error: ' . $e->getMessage()
                         ];
                     }
                 }

                 return [
                     'success' => $successCount > 0,
                     'message' => "{$successCount} BPS record(s) created successfully out of {$totalRecords} attempted",
                     'summary' => [
                         'total_students' => count($students),
                         'total_records_attempted' => $totalRecords,
                         'successful_records' => $successCount,
                         'failed_records' => $totalRecords - $successCount,
                         'detention_records_created' => count($detentionRecords)
                     ],
                     'bps_info' => [
                         'case_type' => $caseType,
                         'item_type' => strtoupper($itemType),
                         'item_title' => $disciplineItem->item_title,
                         'item_point' => $disciplineItem->item_point,
                         'academic_year_id' => $academicYearId,
                         'academic_semester' => $academicSemester,
                         'branch_id' => $branchId,
                         'date' => $date
                     ],
                     'results' => $results,
                     'detention_records' => $detentionRecords,
                     'csrf_token' => csrf_token(),
                     'timestamp' => now()->toISOString()
                 ];

             } catch (\Exception $e) {
                 return [
                     'success' => false,
                     'message' => 'Server error occurred while processing BPS records',
                     'error_code' => 'SERVER_ERROR',
                     'error_details' => $e->getMessage(),
                     'line' => $e->getLine(),
                     'file' => basename($e->getFile())
                 ];
             }
         }

         /**
          * Calculate BPS points and create detention records if necessary
          * Adapted from web BPS repository logic
          *
          * @param array $data - BPS data
          * @param int $studentId - Student ID
          * @param array $notiArray - Notification array (unused in mobile)
          * @param DisciplineRecord $record - The discipline record
          * @return array|null - Detention information if created
          */
         private function calculateBpsPoint($data, $studentId, $notiArray, $record) {
             try {
                 $branchId = $data['branch_id'];
                 $academicYearId = $this->ah->branchAcademicYear($branchId);

                 // Get last detention record
                 $detentionRecord = DetentionRecord::where('student_id', $studentId)
                     ->where('academic_year_id', $academicYearId)
                     ->where('branch_id', $branchId)
                     ->latest()->first();

                 if ($detentionRecord == null) {
                     // Get discipline records for all semester
                     $disciplineRecords = DisciplineRecord::with('item')
                         ->where('student_id', $studentId)
                         ->where('academic_year_id', $academicYearId)
                         ->where('branch_id', $branchId)
                         ->where('item_type', 'dps')
                         ->get();

                     // Calculate total point
                     $totalPoint = 0;
                     foreach ($disciplineRecords as $disciplineRecord) {
                         $totalPoint += $disciplineRecord->item_point;
                     }

                     $totalPointForCalculation = $totalPoint;

                     // If calculation point greater than 5, create detention
                     if ($totalPointForCalculation <= -5) {
                         $count = intdiv(abs($totalPointForCalculation), 5);
                         $dataArray = [
                             'dps_record_id' => $record->discipline_record_id,
                             'student_id' => $studentId,
                             'academic_year_id' => $academicYearId,
                             'branch_id' => $branchId,
                             'academic_semester' => $this->ah->currentSemester(),
                             'latest_point' => $totalPoint,
                             'date' => $data['date'],
                         ];

                         for ($i = 1; $i <= $count; $i++) {
                             $quotient = intdiv($i, 3);
                             $remainder = $i % 3;

                             if ($quotient % 3 == 1 && $remainder == 0) {
                                 $dataArray['detention_type'] = 'ASD';
                                 $dataArray['system_note'] = 'Required to attend After School Detention';
                                 DetentionRecord::create($dataArray);
                             }
                             if ($quotient % 3 == 2 && $remainder == 0) {
                                 $dataArray['detention_type'] = 'SD';
                                 $dataArray['system_note'] = 'Required to attend Saturday Detention';
                                 DetentionRecord::create($dataArray);
                             }
                             if ($quotient % 3 == 0 && $remainder == 0) {
                                 $dataArray['detention_type'] = 'OSSD';
                                 $dataArray['system_note'] = 'Required to attend Out-of-School Suspension Detention';
                                 DetentionRecord::create($dataArray);
                             }
                         }

                         return [
                             'student_id' => $studentId,
                             'total_point' => $totalPoint,
                             'detention_count' => $count,
                             'detention_created' => true
                         ];
                     }
                 } else {
                     // Handle case where detention record exists
                     $disciplineRecords = DisciplineRecord::with('item')
                         ->where('student_id', $studentId)
                         ->where('academic_year_id', $academicYearId)
                         ->where('branch_id', $branchId)
                         ->where('item_type', 'dps')
                         ->where('discipline_record_id', '>', $detentionRecord->dps_record_id)
                         ->get();

                     $totalPoint = $detentionRecord->latest_point;
                     $remainingPoints = 0;
                     foreach ($disciplineRecords as $disciplineRecord) {
                         $remainingPoints += $disciplineRecord->item_point;
                     }

                     $totalPointForCalculation = $totalPoint;
                     $totalPointForCalculation += $remainingPoints;

                     if ($totalPointForCalculation <= -5) {
                         $count = intdiv(abs($totalPointForCalculation), 5);
                         $dataArray = [
                             'dps_record_id' => $record->discipline_record_id,
                             'student_id' => $studentId,
                             'academic_year_id' => $academicYearId,
                             'branch_id' => $branchId,
                             'academic_semester' => $this->ah->currentSemester(),
                             'latest_point' => $totalPoint + $remainingPoints,
                             'date' => $data['date'],
                         ];

                         for ($i = 1; $i <= $count; $i++) {
                             $quotient = intdiv($i, 3);
                             $remainder = $i % 3;

                             if ($quotient % 3 == 1 && $remainder == 0) {
                                 $dataArray['detention_type'] = 'ASD';
                                 $dataArray['system_note'] = 'Required to attend After School Detention';
                                 DetentionRecord::create($dataArray);
                             }
                             if ($quotient % 3 == 2 && $remainder == 0) {
                                 $dataArray['detention_type'] = 'SD';
                                 $dataArray['system_note'] = 'Required to attend Saturday Detention';
                                 DetentionRecord::create($dataArray);
                             }
                             if ($quotient % 3 == 0 && $remainder == 0) {
                                 $dataArray['detention_type'] = 'OSSD';
                                 $dataArray['system_note'] = 'Required to attend Out-of-School Suspension Detention';
                                 DetentionRecord::create($dataArray);
                             }
                         }

                         return [
                             'student_id' => $studentId,
                             'total_point' => $totalPoint + $remainingPoints,
                             'detention_count' => $count,
                             'detention_created' => true
                         ];
                     }
                 }

                 return null; // No detention created

             } catch (\Exception $e) {
                 // Don't fail the main BPS creation
                 return null;
             }
         }

         /**
          * Get comprehensive student library data including borrowed books, history, and overdue information
          *
          * @param string $authCode - Student authentication code
          * @return \Illuminate\Http\JsonResponse
          */
         public function getStudentLibraryData($authCode) {
             try {
                 $device = MobileDevice::where('auth_code', $authCode)->first();

                 if (!$device) {
                     return response()->json(['error' => 'Invalid authentication code'], 401);
                 }

                 // Get student information
                 $student = User::where('id', $device->student_id)
                               ->where('user_status', 1)
                               ->first();

                 if (!$student) {
                     return response()->json(['error' => 'Student not found'], 404);
                 }

                 $branchId = $student->branch_id;
                 $academicYearId = $this->ah->branchAcademicYear($branchId);
                 $currentDate = date('Y-m-d');

                 // Get currently borrowed books (not returned)
                 $currentlyBorrowed = $this->getCurrentlyBorrowedBooks($device->student_id, $branchId);

                 // Get overdue books
                 $overdueBooks = $this->getOverdueBooks($device->student_id, $branchId, $currentDate);

                 // Get library history (all borrowed books)
                 $libraryHistory = $this->getLibraryHistory($device->student_id, $branchId);

                 // Get library statistics
                 $libraryStats = $this->getLibraryStatistics($device->student_id, $branchId, $academicYearId);

                 // Get available books for browsing (optional - top 20 recent additions)
                 $availableBooks = $this->getAvailableBooks($branchId, 20);

                 return response()->json([
                     'success' => true,
                     'student_info' => [
                         'student_id' => $student->id,
                         'student_name' => $student->name,
                         'student_photo' => $student->photo,
                         'branch_id' => $branchId
                     ],
                     'currently_borrowed' => $currentlyBorrowed,
                     'overdue_books' => $overdueBooks,
                     'library_history' => $libraryHistory,
                     'library_statistics' => $libraryStats,
                     'available_books' => $availableBooks,
                     'summary' => [
                         'total_currently_borrowed' => count($currentlyBorrowed),
                         'total_overdue' => count($overdueBooks),
                         'total_history_records' => count($libraryHistory),
                         'can_borrow_more' => $libraryStats['can_borrow_more'],
                         'borrowing_limit' => $libraryStats['borrowing_limit'],
                         'remaining_limit' => $libraryStats['remaining_limit']
                     ],
                     'generated_at' => now()->toISOString()
                 ]);

             } catch (\Exception $e) {
                 return response()->json([
                     'success' => false,
                     'error' => 'Server error occurred while fetching library data',
                     'error_details' => $e->getMessage(),
                     'line' => $e->getLine(),
                     'file' => basename($e->getFile())
                 ], 500);
             }
         }

         /**
          * Get currently borrowed books (not returned)
          */
         private function getCurrentlyBorrowedBooks($studentId, $branchId) {
             return \DB::table('library_issues')
                 ->leftJoin('library_books', 'library_issues.book_id', 'library_books.book_id')
                 ->leftJoin('library_authors', 'library_books.author_id', 'library_authors.author_id')
                 ->leftJoin('library_category', 'library_books.category_id', 'library_category.category_id')
                 ->leftJoin('library_publication_type', 'library_books.publication_type_id', 'library_publication_type.publication_id')
                 ->where('library_issues.user_id', $studentId)
                 ->where('library_issues.branch_id', $branchId)
                 ->where('library_issues.is_returned', 0)
                 ->select(
                     'library_issues.issue_id',
                     'library_issues.issue_date',
                     'library_issues.should_return_date',
                     'library_issues.is_renewed',
                     'library_issues.issue_type',
                     'library_books.book_id',
                     'library_books.title',
                     'library_books.barcode',
                     'library_books.isbn',
                     'library_books.publication_year',
                     'library_books.page_count',
                     'library_books.cover_photo',
                     'library_books.location',
                     'library_books.call_number',
                     'library_authors.author_name',
                     'library_category.category_name',
                     'library_publication_type.publication_name',
                     \DB::raw('DATEDIFF(library_issues.should_return_date, CURDATE()) as days_until_due'),
                     \DB::raw('CASE WHEN library_issues.should_return_date < CURDATE() THEN 1 ELSE 0 END as is_overdue')
                 )
                 ->orderBy('library_issues.issue_date', 'desc')
                 ->get()
                 ->map(function ($book) {
                     return [
                         'issue_id' => $book->issue_id,
                         'book_id' => $book->book_id,
                         'title' => $book->title,
                         'author_name' => $book->author_name ?? 'Unknown Author',
                         'category_name' => $book->category_name ?? 'Uncategorized',
                         'publication_type' => $book->publication_name ?? 'Book',
                         'barcode' => $book->barcode,
                         'isbn' => $book->isbn,
                         'publication_year' => $book->publication_year,
                         'page_count' => $book->page_count,
                         'cover_photo' => $book->cover_photo ? "https://sis.bfi.edu.mm" . $book->cover_photo : null,
                         'location' => $book->location,
                         'call_number' => $book->call_number,
                         'issue_date' => $book->issue_date,
                         'should_return_date' => $book->should_return_date,
                         'days_until_due' => $book->days_until_due,
                         'is_overdue' => $book->is_overdue == 1,
                         'is_renewed' => $book->is_renewed == 1,
                         'issue_type' => $book->issue_type,
                         'status' => $book->is_overdue == 1 ? 'overdue' : ($book->days_until_due <= 3 ? 'due_soon' : 'normal')
                     ];
                 })
                 ->toArray();
         }

         /**
          * Get overdue books
          */
         private function getOverdueBooks($studentId, $branchId, $currentDate) {
             return \DB::table('library_issues')
                 ->leftJoin('library_books', 'library_issues.book_id', 'library_books.book_id')
                 ->leftJoin('library_authors', 'library_books.author_id', 'library_authors.author_id')
                 ->leftJoin('library_category', 'library_books.category_id', 'library_category.category_id')
                 ->where('library_issues.user_id', $studentId)
                 ->where('library_issues.branch_id', $branchId)
                 ->where('library_issues.is_returned', 0)
                 ->where('library_issues.should_return_date', '<', $currentDate)
                 ->select(
                     'library_issues.issue_id',
                     'library_issues.issue_date',
                     'library_issues.should_return_date',
                     'library_books.book_id',
                     'library_books.title',
                     'library_books.barcode',
                     'library_books.cover_photo',
                     'library_authors.author_name',
                     'library_category.category_name',
                     \DB::raw('DATEDIFF(CURDATE(), library_issues.should_return_date) as days_overdue')
                 )
                 ->orderBy('days_overdue', 'desc')
                 ->get()
                 ->map(function ($book) {
                     return [
                         'issue_id' => $book->issue_id,
                         'book_id' => $book->book_id,
                         'title' => $book->title,
                         'author_name' => $book->author_name ?? 'Unknown Author',
                         'category_name' => $book->category_name ?? 'Uncategorized',
                         'barcode' => $book->barcode,
                         'cover_photo' => $book->cover_photo ? "https://sis.bfi.edu.mm" . $book->cover_photo : null,
                         'issue_date' => $book->issue_date,
                         'should_return_date' => $book->should_return_date,
                         'days_overdue' => $book->days_overdue,
                         'overdue_status' => $book->days_overdue > 30 ? 'severely_overdue' : ($book->days_overdue > 7 ? 'moderately_overdue' : 'recently_overdue')
                     ];
                 })
                 ->toArray();
         }

         /**
          * Get library history (all borrowed books including returned ones)
          */
         private function getLibraryHistory($studentId, $branchId, $limit = 50) {
             return \DB::table('library_issues')
                 ->leftJoin('library_books', 'library_issues.book_id', 'library_books.book_id')
                 ->leftJoin('library_authors', 'library_books.author_id', 'library_authors.author_id')
                 ->leftJoin('library_category', 'library_books.category_id', 'library_category.category_id')
                 ->where('library_issues.user_id', $studentId)
                 ->where('library_issues.branch_id', $branchId)
                 ->select(
                     'library_issues.issue_id',
                     'library_issues.issue_date',
                     'library_issues.should_return_date',
                     'library_issues.return_date',
                     'library_issues.is_returned',
                     'library_issues.is_renewed',
                     'library_issues.issue_type',
                     'library_books.book_id',
                     'library_books.title',
                     'library_books.barcode',
                     'library_books.cover_photo',
                     'library_authors.author_name',
                     'library_category.category_name',
                     \DB::raw('CASE WHEN library_issues.is_returned = 1 THEN DATEDIFF(library_issues.return_date, library_issues.should_return_date) ELSE DATEDIFF(CURDATE(), library_issues.should_return_date) END as days_difference')
                 )
                 ->orderBy('library_issues.issue_date', 'desc')
                 ->limit($limit)
                 ->get()
                 ->map(function ($book) {
                     $status = 'returned';
                     if ($book->is_returned == 0) {
                         $status = $book->days_difference < 0 ? 'current' : 'overdue';
                     } else {
                         $status = $book->days_difference <= 0 ? 'returned_on_time' : 'returned_late';
                     }

                     return [
                         'issue_id' => $book->issue_id,
                         'book_id' => $book->book_id,
                         'title' => $book->title,
                         'author_name' => $book->author_name ?? 'Unknown Author',
                         'category_name' => $book->category_name ?? 'Uncategorized',
                         'barcode' => $book->barcode,
                         'cover_photo' => $book->cover_photo ? "https://sis.bfi.edu.mm" . $book->cover_photo : null,
                         'issue_date' => $book->issue_date,
                         'should_return_date' => $book->should_return_date,
                         'return_date' => $book->return_date,
                         'is_returned' => $book->is_returned == 1,
                         'is_renewed' => $book->is_renewed == 1,
                         'issue_type' => $book->issue_type,
                         'status' => $status,
                         'days_difference' => $book->days_difference
                     ];
                 })
                 ->toArray();
         }

         /**
          * Get library statistics for the student
          */
         private function getLibraryStatistics($studentId, $branchId, $academicYearId) {
             // Get borrowing limits based on user type
             $user = User::find($studentId);
             $borrowingLimit = 2; // Default for students
             if ($user && $user->user_type === 'staff') {
                 $borrowingLimit = 5;
             }

             // Count currently borrowed books
             $currentlyBorrowedCount = \DB::table('library_issues')
                 ->where('user_id', $studentId)
                 ->where('branch_id', $branchId)
                 ->where('is_returned', 0)
                 ->count();

             // Count total books borrowed this academic year
             $totalBorrowedThisYear = \DB::table('library_issues')
                 ->where('user_id', $studentId)
                 ->where('branch_id', $branchId)
                 ->whereYear('issue_date', date('Y'))
                 ->count();

             // Count overdue books
             $overdueCount = \DB::table('library_issues')
                 ->where('user_id', $studentId)
                 ->where('branch_id', $branchId)
                 ->where('is_returned', 0)
                 ->where('should_return_date', '<', date('Y-m-d'))
                 ->count();

             // Count returned books
             $returnedCount = \DB::table('library_issues')
                 ->where('user_id', $studentId)
                 ->where('branch_id', $branchId)
                 ->where('is_returned', 1)
                 ->count();

             // Count renewed books
             $renewedCount = \DB::table('library_issues')
                 ->where('user_id', $studentId)
                 ->where('branch_id', $branchId)
                 ->where('is_renewed', 1)
                 ->count();

             return [
                 'borrowing_limit' => $borrowingLimit,
                 'currently_borrowed' => $currentlyBorrowedCount,
                 'remaining_limit' => max(0, $borrowingLimit - $currentlyBorrowedCount),
                 'can_borrow_more' => $currentlyBorrowedCount < $borrowingLimit,
                 'total_borrowed_this_year' => $totalBorrowedThisYear,
                 'total_returned' => $returnedCount,
                 'total_overdue' => $overdueCount,
                 'total_renewed' => $renewedCount,
                 'borrowing_rate' => $totalBorrowedThisYear > 0 ? round(($returnedCount / $totalBorrowedThisYear) * 100, 1) : 0
             ];
         }

         /**
          * Get available books for browsing
          */
         private function getAvailableBooks($branchId, $limit = 20) {
             return \DB::table('library_books')
                 ->leftJoin('library_authors', 'library_books.author_id', 'library_authors.author_id')
                 ->leftJoin('library_category', 'library_books.category_id', 'library_category.category_id')
                 ->leftJoin('library_publication_type', 'library_books.publication_type_id', 'library_publication_type.publication_id')
                 ->leftJoin('library_issues', function($join) {
                     $join->on('library_books.book_id', '=', 'library_issues.book_id')
                          ->where('library_issues.is_returned', '=', 0);
                 })
                 ->where('library_books.branch_id', $branchId)
                 ->where('library_books.status', 1)
                 ->whereNull('library_issues.issue_id') // Not currently issued
                 ->select(
                     'library_books.book_id',
                     'library_books.title',
                     'library_books.barcode',
                     'library_books.isbn',
                     'library_books.publication_year',
                     'library_books.page_count',
                     'library_books.cover_photo',
                     'library_books.location',
                     'library_books.call_number',
                     'library_books.is_allowed_outside_library',
                     'library_authors.author_name',
                     'library_category.category_name',
                     'library_publication_type.publication_name'
                 )
                 ->orderBy('library_books.created_at', 'desc')
                 ->limit($limit)
                 ->get()
                 ->map(function ($book) {
                     return [
                         'book_id' => $book->book_id,
                         'title' => $book->title,
                         'author_name' => $book->author_name ?? 'Unknown Author',
                         'category_name' => $book->category_name ?? 'Uncategorized',
                         'publication_type' => $book->publication_name ?? 'Book',
                         'barcode' => $book->barcode,
                         'isbn' => $book->isbn,
                         'publication_year' => $book->publication_year,
                         'page_count' => $book->page_count,
                         'cover_photo' => $book->cover_photo ? "https://sis.bfi.edu.mm" . $book->cover_photo : null,
                         'location' => $book->location,
                         'call_number' => $book->call_number,
                         'is_allowed_outside_library' => $book->is_allowed_outside_library == 1,
                         'availability_status' => 'available'
                     ];
                 })
                 ->toArray();
         }

    /**
     * ========================================
     * MOBILE MESSAGING API METHODS
     * ========================================
     */

    /**
     * Get conversations list for mobile user
     */
    public function getConversations($authCode) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            $userId = $device->student_id;

            // Get conversations where user is a member
            $conversations = \App\Models\ChatUser::leftJoin('msg_chat', 'msg_chat.chat_id', 'msg_chat_users.chat_id')
                ->leftJoin('users', 'users.id', 'msg_chat.created_by')
                ->where('msg_chat_users.user_id', $userId)
                ->where('msg_chat.chat_status', 1)
                ->select([
                    'msg_chat.chat_id',
                    'msg_chat.chat_uuid',
                    'msg_chat.chat_topic',
                    'msg_chat.created_by',
                    'msg_chat.created_at',
                    'msg_chat.updated_at',
                    'users.name as creator_name'
                ])
                ->orderBy('msg_chat.updated_at', 'desc')
                ->get();

            $formattedConversations = [];
            foreach ($conversations as $conversation) {
                // Get last message
                $lastMessage = \App\Models\ChatMessage::where('chat_id', $conversation->chat_id)
                    ->orderBy('created_at', 'desc')
                    ->first();

                // Get conversation members
                $members = \App\Models\ChatUser::leftJoin('users', 'users.id', 'msg_chat_users.user_id')
                    ->where('msg_chat_users.chat_id', $conversation->chat_id)
                    ->select([
                        'users.id',
                        'users.name',
                        'users.user_type',
                        'users.photo'
                    ])
                    ->get();

                // Group members by user type
                $groupedMembers = [];
                foreach ($members as $member) {
                    $memberUserType = $member->user_type;

                    if (!isset($groupedMembers[$memberUserType])) {
                        $groupedMembers[$memberUserType] = [
                            'type' => $memberUserType,
                            'type_label' => ucfirst($memberUserType),
                            'members' => [],
                            'count' => 0
                        ];
                    }

                    $groupedMembers[$memberUserType]['members'][] = [
                        'id' => $member->id,
                        'name' => $member->name,
                        'user_type' => $member->user_type,
                        'photo' => $member->photo ? "https://sis.bfi.edu.mm" . $member->photo : null
                    ];

                    $groupedMembers[$memberUserType]['count']++;
                }

                // Sort grouped members by user type priority
                $userTypePriority = ['staff' => 1, 'teacher' => 2, 'parent' => 3, 'student' => 4];
                $sortedMemberGroups = collect($groupedMembers)->sortBy(function($group) use ($userTypePriority) {
                    return $userTypePriority[$group['type']] ?? 999;
                })->values()->toArray();

                // Count unread messages - messages created after user's last read time
                $chatUser = \App\Models\ChatUser::where('chat_id', $conversation->chat_id)
                    ->where('user_id', $userId)
                    ->first();

                $lastReadTime = $chatUser ? $chatUser->updated_at : null;

                $unreadCount = \App\Models\ChatMessage::where('chat_id', $conversation->chat_id)
                    ->where('user_id', '!=', $userId)
                    ->when($lastReadTime, function($query) use ($lastReadTime) {
                        return $query->where('created_at', '>', $lastReadTime);
                    })
                    ->count();

                $formattedConversations[] = [
                    'conversation_id' => $conversation->chat_id,
                    'conversation_uuid' => $conversation->chat_uuid,
                    'topic' => $conversation->chat_topic,
                    'creator' => [
                        'id' => $conversation->created_by,
                        'name' => $conversation->creator_name
                    ],
                    'members' => $members->map(function($member) {
                        return [
                            'id' => $member->id,
                            'name' => $member->name,
                            'user_type' => $member->user_type,
                            'photo' => $member->photo ? "https://sis.bfi.edu.mm" . $member->photo : null
                        ];
                    }),
                    'grouped_members' => $sortedMemberGroups,
                    'last_message' => $lastMessage ? [
                        'message_id' => $lastMessage->message_id,
                        'content' => $lastMessage->message,
                        'sender_id' => $lastMessage->user_id,
                        'created_at' => $lastMessage->created_at,
                        'message_type' => $lastMessage->message_type ?? 'text'
                    ] : null,
                    'unread_count' => $unreadCount,
                    'created_at' => $conversation->created_at,
                    'updated_at' => $conversation->updated_at
                ];
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'conversations' => $formattedConversations,
                    'total_count' => count($formattedConversations)
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to retrieve conversations'], 500);
        }
    }

    /**
     * Get messages in a conversation
     */
    public function getConversationMessages($authCode, $conversationUuid, $page = 1, $limit = 50) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            $userId = $device->student_id;

            // Verify user is member of this conversation
            $conversation = \App\Models\Chat::where('chat_uuid', $conversationUuid)->first();
            if (!$conversation) {
                return response()->json(['error' => 'Conversation not found'], 404);
            }

            $isMember = \App\Models\ChatUser::where('chat_id', $conversation->chat_id)
                ->where('user_id', $userId)
                ->exists();

            if (!$isMember) {
                return response()->json(['error' => 'Access denied to this conversation'], 403);
            }

            // Get messages with pagination
            $offset = ($page - 1) * $limit;
            $messages = \App\Models\ChatMessage::leftJoin('users', 'users.id', 'msg_messages.user_id')
                ->where('msg_messages.chat_id', $conversation->chat_id)
                ->select([
                    'msg_messages.message_id',
                    'msg_messages.message',
                    'msg_messages.user_id',
                    'msg_messages.created_at',
                    'msg_messages.message_type',
                    'msg_messages.attachment_url',
                    'users.name as sender_name',
                    'users.user_type as sender_type',
                    'users.photo as sender_photo'
                ])
                ->orderBy('msg_messages.created_at', 'desc')
                ->offset($offset)
                ->limit($limit)
                ->get();

            // Get user's last read time for this conversation
            $chatUser = \App\Models\ChatUser::where('chat_id', $conversation->chat_id)
                ->where('user_id', $userId)
                ->first();
            $lastReadTime = $chatUser ? $chatUser->updated_at : null;

            $formattedMessages = $messages->map(function($message) use ($userId, $lastReadTime) {
                // Check if message is read by current user
                $isRead = $message->user_id == $userId || // Own messages are always "read"
                         ($lastReadTime && $message->created_at <= $lastReadTime);

                return [
                    'message_id' => $message->message_id,
                    'content' => $message->message,
                    'message_type' => $message->message_type ?? 'text',
                    'attachment_url' => $message->attachment_url,
                    'sender' => [
                        'id' => $message->user_id,
                        'name' => $message->sender_name,
                        'user_type' => $message->sender_type,
                        'photo' => $message->sender_photo ? "https://sis.bfi.edu.mm" . $message->sender_photo : null
                    ],
                    'created_at' => $message->created_at,
                    'is_own_message' => $message->user_id == $userId,
                    'is_read' => $isRead
                ];
            });

            // Get total message count for pagination
            $totalMessages = \App\Models\ChatMessage::where('chat_id', $conversation->chat_id)->count();
            $totalPages = ceil($totalMessages / $limit);

            return response()->json([
                'success' => true,
                'data' => [
                    'conversation' => [
                        'id' => $conversation->chat_id,
                        'uuid' => $conversation->chat_uuid,
                        'topic' => $conversation->chat_topic
                    ],
                    'messages' => $formattedMessages,
                    'pagination' => [
                        'current_page' => $page,
                        'per_page' => $limit,
                        'total_messages' => $totalMessages,
                        'total_pages' => $totalPages,
                        'has_more' => $page < $totalPages
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to retrieve messages'], 500);
        }
    }

    /**
     * Send a message to a conversation
     */
    public function sendMessage($authCode, $data) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            $userId = $device->student_id;

            // Validate required fields
            if (!isset($data['conversation_uuid']) || !isset($data['message_content'])) {
                return response()->json(['error' => 'conversation_uuid and message_content are required'], 400);
            }

            // Verify conversation exists and user is member
            $conversation = \App\Models\Chat::where('chat_uuid', $data['conversation_uuid'])->first();
            if (!$conversation) {
                return response()->json(['error' => 'Conversation not found'], 404);
            }

            $isMember = \App\Models\ChatUser::where('chat_id', $conversation->chat_id)
                ->where('user_id', $userId)
                ->exists();

            if (!$isMember) {
                return response()->json(['error' => 'Access denied to this conversation'], 403);
            }

            \DB::beginTransaction();

            // Create message
            $message = \App\Models\ChatMessage::create([
                'chat_id' => $conversation->chat_id,
                'user_id' => $userId,
                'message' => $data['message_content'],
                'message_type' => $data['message_type'] ?? 'text',
                'attachment_url' => $data['attachment_url'] ?? null
            ]);

            // Update sender's last read time in chat_users table
            \App\Models\ChatUser::where('chat_id', $conversation->chat_id)
                ->where('user_id', $userId)
                ->update(['updated_at' => now()]);

            // Update conversation timestamp
            $conversation->touch();

            // Send notifications to other members
            $members = \App\Models\ChatUser::where('chat_id', $conversation->chat_id)
                ->where('user_id', '!=', $userId)
                ->get();

            $sender = User::find($userId);
            $notificationResults = [];

            \Log::info('Message notification setup', [
                'sender_id' => $userId,
                'conversation_id' => $conversation->chat_id,
                'total_members_found' => $members->count(),
                'member_ids' => $members->pluck('user_id')->toArray()
            ]);

            foreach ($members as $member) {
                // Skip if member is the sender (double-check to prevent sender from receiving own notification)
                if ($member->user_id == $userId) {
                    \Log::info('Skipping notification for sender', [
                        'sender_id' => $userId,
                        'member_id' => $member->user_id
                    ]);
                    continue;
                }

                try {
                    // Create web notification
                    $this->notification->create([
                        'detail_id' => $conversation->chat_id,
                        'description' => $conversation->chat_uuid,
                        'user' => $member->user_id,
                        'type' => 'message',
                        'notification_content' => $sender->name . ' sent message to ' . $conversation->chat_topic . ' conversation.'
                    ]);

                    // Send mobile notification to all members (students and staff) except sender
                    $memberUser = User::find($member->user_id);
                    if ($memberUser) {
                        \Log::info('Sending message notification', [
                            'sender_id' => $userId,
                            'sender_name' => $sender->name,
                            'receiver_id' => $member->user_id,
                            'receiver_type' => $memberUser->user_type,
                            'conversation_id' => $conversation->chat_id,
                            'conversation_topic' => $conversation->chat_topic
                        ]);

                        // Use sendRealTime for immediate delivery instead of sendSingle (queued)
                        $notificationResult = $this->mobileNotification->sendRealTime([
                            'title' => 'New Message',
                            'message' => $sender->name . ' sent message to ' . $conversation->chat_topic . ' conversation.',
                            'student' => $member->user_id, // This field name is used for any user type
                            'type' => 'message',
                            'user_type' => $memberUser->user_type,
                            'priority' => 'normal',
                            'category' => 'messaging'
                        ]);

                        \Log::info('Message notification result', [
                            'receiver_id' => $member->user_id,
                            'notification_sent' => $notificationResult
                        ]);
                    }

                    $notificationResults[] = [
                        'user_id' => $member->user_id,
                        'notification_sent' => true
                    ];
                } catch (\Exception $e) {

                    $notificationResults[] = [
                        'user_id' => $member->user_id,
                        'notification_sent' => false,
                        'error' => $e->getMessage()
                    ];
                }
            }

            \DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Message sent successfully',
                'data' => [
                    'message_id' => $message->message_id,
                    'conversation_uuid' => $conversation->chat_uuid,
                    'content' => $message->message,
                    'message_type' => $message->message_type,
                    'created_at' => $message->created_at,
                    'sender' => [
                        'id' => $sender->id,
                        'name' => $sender->name,
                        'user_type' => $sender->user_type
                    ]
                ],
                'notifications' => $notificationResults
            ]);

        } catch (\Exception $e) {
            \DB::rollBack();
            return response()->json(['error' => 'Failed to send message'], 500);
        }
    }

    /**
     * Create a new conversation
     */
    public function createConversation($authCode, $data) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            $userId = $device->student_id;

            // Validate required fields
            if (!isset($data['topic']) || !isset($data['members']) || !is_array($data['members'])) {
                return response()->json(['error' => 'topic and members array are required'], 400);
            }

            if (empty($data['members'])) {
                return response()->json(['error' => 'At least one member is required'], 400);
            }

            \DB::beginTransaction();

            // Create conversation
            $conversation = \App\Models\Chat::create([
                'chat_uuid' => Str::uuid(),
                'chat_topic' => $data['topic'],
                'created_by' => $userId,
                'chat_status' => 1
            ]);

            // Add creator as member
            \App\Models\ChatUser::create([
                'chat_id' => $conversation->chat_id,
                'user_id' => $userId
            ]);

            // Add other members
            $notificationResults = [];
            $creator = User::find($userId);

            foreach ($data['members'] as $memberId) {
                if ($memberId != $userId) {
                    \App\Models\ChatUser::create([
                        'chat_id' => $conversation->chat_id,
                        'user_id' => $memberId
                    ]);

                    // Send notifications
                    try {
                        $this->notification->create([
                            'detail_id' => $conversation->chat_id,
                            'description' => $conversation->chat_uuid,
                            'user' => $memberId,
                            'type' => 'message',
                            'notification_content' => $creator->name . ' created new conversation with you.'
                        ]);

                        $member = User::find($memberId);
                        if ($member) {
                            $this->mobileNotification->sendRealTime([
                                'title' => 'New Conversation',
                                'message' => $creator->name . ' created new conversation: ' . $conversation->chat_topic,
                                'student' => $memberId,
                                'type' => 'message',
                                'user_type' => $member->user_type,
                                'priority' => 'normal',
                                'category' => 'messaging'
                            ]);
                        }

                        $notificationResults[] = [
                            'user_id' => $memberId,
                            'notification_sent' => true
                        ];
                    } catch (\Exception $e) {

                        $notificationResults[] = [
                            'user_id' => $memberId,
                            'notification_sent' => false,
                            'error' => $e->getMessage()
                        ];
                    }
                }
            }

            \DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Conversation created successfully',
                'data' => [
                    'conversation_id' => $conversation->chat_id,
                    'conversation_uuid' => $conversation->chat_uuid,
                    'topic' => $conversation->chat_topic,
                    'created_by' => $userId,
                    'created_at' => $conversation->created_at
                ],
                'notifications' => $notificationResults
            ]);

        } catch (\Exception $e) {
            \DB::rollBack();
            return response()->json(['error' => 'Failed to create conversation'], 500);
        }
    }

    /**
     * Get available users for messaging - DEPRECATED
     * Use getAvailableUsersForStudent() or getAvailableUsersForStaff() instead
     */
    public function getAvailableUsers($authCode, $userType = null) {
        $device = MobileDevice::where('auth_code', $authCode)->first();

        if (!$device) {
            return response()->json(['error' => 'Invalid authentication code'], 401);
        }

        $currentUser = User::find($device->student_id);
        if (!$currentUser) {
            return response()->json(['error' => 'User not found'], 404);
        }

        // Route to appropriate method based on user type
        if ($currentUser->user_type === 'student') {
            return $this->getAvailableUsersForStudent($authCode, $userType);
        } elseif ($currentUser->user_type === 'staff') {
            return $this->getAvailableUsersForStaff($authCode, $userType);
        }

        return response()->json(['error' => 'Invalid user type'], 400);
    }

    /**
     * Get available users for messaging - STUDENT VERSION (Restricted Access)
     */
    public function getAvailableUsersForStudent($authCode, $userType = null) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            $currentUser = User::find($device->student_id);
            if (!$currentUser || $currentUser->user_type !== 'student') {
                return response()->json(['error' => 'Student authentication required'], 403);
            }

            // Get student branch information for mobile API context
            $branch = StudentInformation::where("id", $device->student_id)->first();
            if (!$branch) {
                return response()->json(['error' => 'Student information not found'], 404);
            }

            $academicYearId = $this->ah->branchAcademicYear($branch->branch_id);
            if (!$academicYearId) {
                return response()->json(['error' => 'Academic year not found for this branch'], 404);
            }

            $branchId = $branch->branch_id;
            $studentId = $currentUser->id;

            $availableUsers = collect();

            // 1. Get homeroom teacher - simplified approach
            $homeroomTeacher = null;

            // First try with academic year
            $studentClassroom = \DB::table('students_classroom')
                ->where('student_id', $studentId)
                ->where('academic_year_id', $academicYearId)
                ->where('branch_id', $branchId)
                ->first();

            if (!$studentClassroom) {
                // Fallback without academic year filter
                $studentClassroom = \DB::table('students_classroom')
                    ->where('student_id', $studentId)
                    ->where('branch_id', $branchId)
                    ->orderBy('academic_year_id', 'desc')
                    ->first();
            }

            if ($studentClassroom) {
                $classroom = \DB::table('classrooms')
                    ->where('classroom_id', $studentClassroom->classroom_id)
                    ->first();

                if ($classroom && $classroom->homeroom_teacher_id) {
                    $homeroomTeacher = User::where('id', $classroom->homeroom_teacher_id)
                        ->where('user_status', 1)
                        ->first();
                }
            }



            if ($homeroomTeacher) {
                $availableUsers->push((object)[
                    'id' => $homeroomTeacher->id,
                    'name' => $homeroomTeacher->name,
                    'user_type' => 'staff',
                    'role' => 'homeroom_teacher',
                    'email' => $homeroomTeacher->email,
                    'photo' => $homeroomTeacher->photo,
                    'branch_id' => $homeroomTeacher->branch_id
                ]);
            }

            // 2. Get subject teachers - improved approach with better fallbacks
            $subjectTeachers = collect();

            // Get student's grades/classes with academic year filter
            $studentGrades = \DB::table('academic_elective_grade_students')
                ->leftJoin('academic_elective_grade', 'academic_elective_grade.grade_id', 'academic_elective_grade_students.grade_id')
                ->where('academic_elective_grade_students.student_id', $studentId)
                ->where('academic_elective_grade.branch_id', $branchId)
                ->where('academic_elective_grade.academic_year_id', $academicYearId)
                ->select('academic_elective_grade.grade_id', 'academic_elective_grade.subject_id', 'academic_elective_grade.grade_name')
                ->distinct()
                ->get();

            // If no grades found with academic year, try without academic year filter
            if ($studentGrades->isEmpty()) {
                $studentGrades = \DB::table('academic_elective_grade_students')
                    ->leftJoin('academic_elective_grade', 'academic_elective_grade.grade_id', 'academic_elective_grade_students.grade_id')
                    ->where('academic_elective_grade_students.student_id', $studentId)
                    ->where('academic_elective_grade.branch_id', $branchId)
                    ->select('academic_elective_grade.grade_id', 'academic_elective_grade.subject_id', 'academic_elective_grade.grade_name')
                    ->distinct()
                    ->get();
            }

            // For each grade/subject combination, find the teachers
            foreach ($studentGrades as $grade) {
                $teachers = \DB::table('academic_timetable')
                    ->leftJoin('users', 'users.id', 'academic_timetable.user_id')
                    ->where('academic_timetable.grade_id', $grade->grade_id)
                    ->where('academic_timetable.subject_id', $grade->subject_id)
                    ->where('academic_timetable.branch_id', $branchId)
                    ->where('users.user_status', 1)
                    ->where('users.user_type', 'staff')
                    ->whereNotNull('users.id')
                    ->select('users.*')
                    ->distinct()
                    ->get();

                foreach ($teachers as $teacher) {
                    // Avoid duplicates
                    if (!$subjectTeachers->contains('id', $teacher->id)) {
                        $subjectTeachers->push($teacher);
                    }
                }
            }

            // If still no teachers found, try a broader search for all teachers in the branch
            if ($subjectTeachers->isEmpty()) {
                $subjectTeachers = \DB::table('academic_timetable')
                    ->leftJoin('users', 'users.id', 'academic_timetable.user_id')
                    ->where('academic_timetable.branch_id', $branchId)
                    ->where('academic_timetable.academic_year_id', $academicYearId)
                    ->where('users.user_status', 1)
                    ->where('users.user_type', 'staff')
                    ->whereNotNull('users.id')
                    ->select('users.*')
                    ->distinct()
                    ->get();
            }

            // Final fallback: get all active staff in the branch
            if ($subjectTeachers->isEmpty()) {
                $subjectTeachers = User::where('user_status', 1)
                    ->where('user_type', 'staff')
                    ->where('branch_id', $branchId)
                    ->get();
            }



            foreach ($subjectTeachers as $teacher) {
                // Only add if not already added with a higher priority role
                if (!$availableUsers->contains('id', $teacher->id)) {
                    $availableUsers->push((object)[
                        'id' => $teacher->id,
                        'name' => $teacher->name,
                        'user_type' => 'staff',
                        'role' => 'subject_teacher',
                        'email' => $teacher->email,
                        'photo' => $teacher->photo,
                        'branch_id' => $teacher->branch_id
                    ]);
                }
            }

            // 3. Get head of school (role-based detection)
            $headOfSchoolRoles = [1, 2, 3]; // Principal, Director, etc.
            $headOfSchoolUserRoles = \DB::table('roles_user')
                ->where('branch_id', $branchId)
                ->whereIn('role_id', $headOfSchoolRoles)
                ->get();

            $headOfSchool = collect();
            foreach ($headOfSchoolUserRoles as $userRole) {
                $user = User::where('id', $userRole->user_id)
                    ->where('user_status', 1)
                    ->where('user_type', 'staff')
                    ->first();

                if ($user && !$headOfSchool->contains('id', $user->id)) {
                    $headOfSchool->push($user);
                }
            }

            // 4. Get head of section/department (role-based detection)
            $headOfSectionRoles = [52, 69, 70, 71, 72]; // Vice principal and department heads
            $headOfSectionUserRoles = \DB::table('roles_user')
                ->where('branch_id', $branchId)
                ->whereIn('role_id', $headOfSectionRoles)
                ->get();

            $headOfSection = collect();
            foreach ($headOfSectionUserRoles as $userRole) {
                $user = User::where('id', $userRole->user_id)
                    ->where('user_status', 1)
                    ->where('user_type', 'staff')
                    ->first();

                if ($user && !$headOfSection->contains('id', $user->id)) {
                    $headOfSection->push($user);
                }
            }

            // Add users with role priority: head_of_school > head_of_section > subject_teacher
            // If a user has multiple roles, they get the highest priority role
            foreach ($headOfSchool as $head) {
                if (!$availableUsers->contains('id', $head->id)) {
                    $availableUsers->push((object)[
                        'id' => $head->id,
                        'name' => $head->name,
                        'user_type' => 'staff',
                        'role' => 'head_of_school',
                        'email' => $head->email,
                        'photo' => $head->photo,
                        'branch_id' => $head->branch_id
                    ]);
                }
            }

            foreach ($headOfSection as $head) {
                if (!$availableUsers->contains('id', $head->id)) {
                    $availableUsers->push((object)[
                        'id' => $head->id,
                        'name' => $head->name,
                        'user_type' => 'staff',
                        'role' => 'head_of_section',
                        'email' => $head->email,
                        'photo' => $head->photo,
                        'branch_id' => $head->branch_id
                    ]);
                }
            }

            // 5. Get librarian (simplified name-based detection for now)
            $librarian = User::where('user_type', 'staff')
                ->where('branch_id', $branchId)
                ->where('user_status', 1)
                ->where(function($q) {
                    $q->where('name', 'LIKE', '%librarian%')
                      ->orWhere('name', 'LIKE', '%library%')
                      ->orWhere('email', 'LIKE', '%library%')
                      ->orWhere('name', 'LIKE', '%lib%');
                })
                ->get();

            foreach ($librarian as $lib) {
                if (!$availableUsers->contains('id', $lib->id)) {
                    $availableUsers->push((object)[
                        'id' => $lib->id,
                        'name' => $lib->name,
                        'user_type' => 'staff',
                        'role' => 'librarian',
                        'email' => $lib->email,
                        'photo' => $lib->photo,
                        'branch_id' => $lib->branch_id
                    ]);
                }
            }

            // 6. Get classmates (ONLY same classroom - not other students)
            $classmates = \DB::table('students_classroom as sc1')
                ->leftJoin('students_classroom as sc2', function($join) use ($academicYearId) {
                    $join->on('sc1.classroom_id', 'sc2.classroom_id')
                         ->where('sc2.academic_year_id', $academicYearId);
                })
                ->leftJoin('users', 'users.id', 'sc2.student_id')
                ->where('sc1.student_id', $studentId)
                ->where('sc1.academic_year_id', $academicYearId)
                ->where('sc2.student_id', '!=', $studentId)
                ->where('users.user_status', 1)
                ->where('users.user_type', 'student')
                ->select('users.*')
                ->distinct()
                ->get();

            foreach ($classmates as $classmate) {
                $availableUsers->push((object)[
                    'id' => $classmate->id,
                    'name' => $classmate->name,
                    'user_type' => 'student',
                    'role' => 'classmate',
                    'email' => $classmate->email,
                    'photo' => $classmate->photo,
                    'branch_id' => $classmate->branch_id
                ]);
            }

            // NOTE: Students can ONLY message:
            // - Their homeroom teacher
            // - Teachers who actually teach them (subject teachers)
            // - Head of school (Principal, Director, etc.)
            // - Head of section/department
            // - Librarian
            // - Their direct classmates (same classroom)
            //
            // Students CANNOT message:
            // - Other students from different classes
            // - Teachers who don't teach them
            // - Random staff members

            // Group ALL users by role and user type for grouped_users array (no filtering)
            $groupedUsers = [];
            $totalCount = 0;

            foreach ($availableUsers as $user) {
                $groupKey = $user->user_type === 'staff' ? $user->role : $user->user_type;

                // Create group if it doesn't exist
                if (!isset($groupedUsers[$groupKey])) {
                    $groupedUsers[$groupKey] = [
                        'type' => $groupKey,
                        'type_label' => $this->getRoleLabel($groupKey),
                        'users' => [],
                        'count' => 0
                    ];
                }

                // Add user to appropriate group
                $groupedUsers[$groupKey]['users'][] = [
                    'id' => $user->id,
                    'name' => $user->name,
                    'user_type' => $user->user_type,
                    'role' => $user->role ?? null,
                    'email' => $user->email,
                    'photo' => $user->photo ? "https://sis.bfi.edu.mm" . $user->photo : null,
                    'branch_id' => $user->branch_id
                ];

                $groupedUsers[$groupKey]['count']++;
                $totalCount++;
            }

            // Sort groups by priority for students
            $rolePriority = [
                'homeroom_teacher' => 1,
                'head_of_school' => 2,
                'head_of_section' => 3,
                'subject_teacher' => 4,
                'librarian' => 5,
                'student' => 6
            ];

            $sortedGroups = collect($groupedUsers)->sortBy(function($group) use ($rolePriority) {
                return $rolePriority[$group['type']] ?? 999;
            })->values()->toArray();

            // Apply user type filter ONLY for backward compatibility flat users array
            $filteredUsers = $availableUsers;
            if ($userType) {
                $filteredUsers = $availableUsers->where('user_type', $userType);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'grouped_users' => $sortedGroups, // Always contains all user types/roles
                    'total_count' => $totalCount, // Total count of all users
                    'filtered_count' => $filteredUsers->count(), // Count after applying user_type filter
                    'user_type' => 'student',
                    'access_level' => 'restricted',
                    'applied_filter' => $userType, // Show what filter was applied
                    'student_info' => [
                        'student_id' => $studentId,
                        'branch_id' => $branchId,
                        'academic_year_id' => $academicYearId
                    ],
                    // Keep backward compatibility - this array respects user_type filter
                    'users' => $filteredUsers->map(function($user) {
                        return [
                            'id' => $user->id,
                            'name' => $user->name,
                            'user_type' => $user->user_type,
                            'role' => $user->role ?? null,
                            'email' => $user->email,
                            'photo' => $user->photo ? "https://sis.bfi.edu.mm" . $user->photo : null,
                            'branch_id' => $user->branch_id
                        ];
                    })->values()->toArray()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to retrieve users'], 500);
        }
    }

    /**
     * Get available users for messaging - STAFF VERSION (Role-Based Access)
     */
    public function getAvailableUsersForStaff(string $authCode, ?string $userType = null): \Illuminate\Http\JsonResponse {
        try {
            // Authenticate and validate staff user
            $authResult = $this->authenticateStaffUser($authCode);
            if ($authResult instanceof \Illuminate\Http\JsonResponse) {
                return $authResult;
            }

            [$currentUser, $staffId] = $authResult;

            // Get staff branches
            $userBranches = $this->getStaffBranches($currentUser, $staffId);
            if ($userBranches instanceof \Illuminate\Http\JsonResponse) {
                return $userBranches;
            }

            // Process branches and get users
            $result = $this->processStaffBranchesForUsers($userBranches, $staffId, $userType);

            return response()->json([
                'success' => true,
                'data' => $result
            ]);

        } catch (\Exception $e) {
            // Log the actual error for debugging
            error_log('getAvailableUsersForStaff error: ' . $e->getMessage());
            error_log('Stack trace: ' . $e->getTraceAsString());

            return response()->json([
                'error' => 'Failed to retrieve users',
                'debug_message' => $e->getMessage(),
                'debug_trace' => $e->getTraceAsString()
            ], 500);
        }
    }

    /**
     * Authenticate staff user for messaging
     */
    private function authenticateStaffUser(string $authCode): array|\Illuminate\Http\JsonResponse {
        $device = MobileDevice::where('auth_code', $authCode)->first();

        if (!$device) {
            return response()->json(['error' => 'Invalid authentication code'], 401);
        }

        $currentUser = User::find($device->student_id);
        if (!$currentUser || $currentUser->user_type !== 'staff') {
            return response()->json(['error' => 'Staff authentication required'], 403);
        }

        return [$currentUser, $currentUser->id];
    }

    /**
     * Get branches for staff member
     */
    private function getStaffBranches(User $currentUser, int $staffId): \Illuminate\Support\Collection|\Illuminate\Http\JsonResponse {
        // Get all branches for this staff member (multi-branch support)
        $userBranches = \DB::table('users_branches')
            ->leftJoin('branches', 'branches.branch_id', 'users_branches.branch_id')
            ->where('user_id', $staffId)
            ->get();

        // If no branches found in users_branches, use the user's default branch
        if ($userBranches->isEmpty()) {
            if ($currentUser->branch_id) {
                $branch = \App\Models\Branch::find($currentUser->branch_id);
                if ($branch) {
                    $userBranches = collect([(object)[
                        'branch_id' => $branch->branch_id,
                        'branch_name' => $branch->branch_name,
                        'branch_description' => $branch->branch_description,
                        'is_active' => 1
                    ]]);
                }
            }
        }

        if ($userBranches->isEmpty()) {
            return response()->json(['error' => 'No branches found for staff member'], 404);
        }

        return $userBranches;
    }

    /**
     * Process staff branches and get available users
     */
    private function processStaffBranchesForUsers(\Illuminate\Support\Collection $userBranches, int $staffId, ?string $userType): array {
        $availableUsers = collect();
        $branchesData = [];
        $overallAccessLevel = 'restricted';
        $overallStaffRole = 'general_staff';

        // Process each branch
        foreach ($userBranches as $userBranch) {
            $branchResult = $this->processSingleBranchForUsers($userBranch, $staffId, $availableUsers);

            // Update overall access level (use highest permission level)
            $accessLevelPriority = ['restricted' => 0, 'subject' => 1, 'homeroom' => 2, 'section' => 3, 'full' => 4];
            if ($accessLevelPriority[$branchResult['access_level']] > $accessLevelPriority[$overallAccessLevel]) {
                $overallAccessLevel = $branchResult['access_level'];
                $overallStaffRole = $branchResult['staff_role'];
            }

            $branchesData[] = $branchResult['branch_data'];
        }

        return $this->formatStaffUsersResponse($availableUsers, $branchesData, $overallAccessLevel, $overallStaffRole, $userType);
    }

    /**
     * Process a single branch for staff users
     */
    private function processSingleBranchForUsers(object $userBranch, int $staffId, \Illuminate\Support\Collection &$availableUsers): array {
        $branchId = $userBranch->branch_id;

        try {
            $academicYearId = $this->ah->branchAcademicYear($branchId);
        } catch (\Exception $e) {
            \Log::error('Error getting academic year for branch: ' . $e->getMessage(), [
                'branchId' => $branchId,
                'userBranch' => $userBranch
            ]);
            $academicYearId = null;
        }

        if (!$academicYearId) {
            return [
                'access_level' => 'restricted',
                'staff_role' => 'general_staff',
                'branch_data' => [
                    'branch_id' => $branchId,
                    'branch_name' => $userBranch->branch_name,
                    'branch_description' => $userBranch->branch_description ?? null,
                    'academic_year_id' => null,
                    'access_level' => 'restricted',
                    'staff_role' => 'general_staff',
                    'users_count' => 0
                ]
            ];
        }

        // Determine staff role and access level for this branch
        $roleResult = $this->determineStaffRoleAndAccess($staffId, $branchId, $academicYearId);

        // Get users based on role and access level for this branch
        $branchUsers = $this->getBranchUsersByAccessLevel($roleResult['access_level'], $branchId, $staffId, $academicYearId);

        // Add branch users to overall collection (avoid duplicates)
        foreach ($branchUsers as $user) {
            $existingUser = $availableUsers->firstWhere('id', $user->id);
            if (!$existingUser) {
                $availableUsers->push($user);
            }
        }

        return [
            'access_level' => $roleResult['access_level'],
            'staff_role' => $roleResult['staff_role'],
            'branch_data' => [
                'branch_id' => $branchId,
                'branch_name' => $userBranch->branch_name,
                'branch_description' => $userBranch->branch_description ?? null,
                'academic_year_id' => $academicYearId,
                'access_level' => $roleResult['access_level'],
                'staff_role' => $roleResult['staff_role'],
                'users_count' => $branchUsers->count()
            ]
        ];
    }

    /**
     * Determine staff role and access level for a branch
     */
    private function determineStaffRoleAndAccess(int $staffId, int $branchId, int $academicYearId): array {
        // Get staff roles for this specific branch
        $staffRoles = \App\Models\UserRole::where('user_id', $staffId)
            ->where('branch_id', $branchId)
            ->pluck('role_id')
            ->toArray();

        // Define role hierarchies and permissions
        $headOfSchoolRoles = [1, 2, 3]; // Adjust these role IDs based on your system
        $headOfSectionRoles = [52, 69, 70, 71, 72]; // Vice principal and department heads

        // Determine staff role and access level for this branch
        if (array_intersect($staffRoles, $headOfSchoolRoles)) {
            return ['staff_role' => 'head_of_school', 'access_level' => 'full'];
        } elseif (array_intersect($staffRoles, $headOfSectionRoles)) {
            return ['staff_role' => 'head_of_section', 'access_level' => 'section'];
        }

        // Check if homeroom teacher in this branch
        $isHomeroomTeacher = \App\Models\Classroom::where('homeroom_teacher_id', $staffId)
            ->where('academic_year_id', $academicYearId)
            ->where('branch_id', $branchId)
            ->exists();

        if ($isHomeroomTeacher) {
            return ['staff_role' => 'homeroom_teacher', 'access_level' => 'homeroom'];
        }

        // Check if subject teacher in this branch
        $isSubjectTeacher = $this->checkSubjectTeacherStatus($staffId, $branchId, $academicYearId);

        if ($isSubjectTeacher) {
            return ['staff_role' => 'subject_teacher', 'access_level' => 'subject'];
        }

        return ['staff_role' => 'general_staff', 'access_level' => 'restricted'];
    }

    /**
     * Check if staff member is a subject teacher
     */
    private function checkSubjectTeacherStatus(int $staffId, int $branchId, int $academicYearId): bool {
        // Check if subject teacher in this branch (with or without academic year)
        $isSubjectTeacher = \DB::table('academic_timetable')
            ->where('user_id', $staffId)
            ->where('branch_id', $branchId)
            ->where(function($query) use ($academicYearId) {
                $query->where('academic_year_id', $academicYearId)
                      ->orWhereNull('academic_year_id');
            })
            ->exists();

        // If not found with academic year, try without it
        if (!$isSubjectTeacher) {
            $isSubjectTeacher = \DB::table('academic_timetable')
                ->where('user_id', $staffId)
                ->where('branch_id', $branchId)
                ->exists();
        }

        // Check if this staff member is actually a teacher by checking if they have any teaching assignments
        if (!$isSubjectTeacher) {
            $isSubjectTeacher = \DB::table('academic_timetable')
                ->where('user_id', $staffId)
                ->exists();
        }

        return $isSubjectTeacher;
    }

    /**
     * Get branch users by access level
     */
    private function getBranchUsersByAccessLevel(string $accessLevel, int $branchId, int $staffId, int $academicYearId): \Illuminate\Support\Collection {
        $branchUsers = collect();

        try {
            switch ($accessLevel) {
                case 'full': // Head of School
                    $this->addAllBranchUsers($branchUsers, $branchId, $staffId);
                    break;

                case 'section': // Head of Section/Department
                    $this->addSectionUsers($branchUsers, $branchId, $staffId, $academicYearId);
                    break;

                case 'homeroom': // Homeroom Teacher
                    $this->addHomeroomUsers($branchUsers, $staffId, $branchId, $academicYearId);
                    break;

                case 'subject': // Subject Teacher
                    $this->addSubjectTeacherUsers($branchUsers, $staffId, $branchId, $academicYearId);
                    break;

                default: // General Staff
                    $this->addGeneralStaffUsers($branchUsers, $branchId, $staffId);
                    break;
            }
        } catch (\Exception $e) {
            \Log::error('Error in getBranchUsersByAccessLevel: ' . $e->getMessage(), [
                'accessLevel' => $accessLevel,
                'branchId' => $branchId,
                'staffId' => $staffId,
                'academicYearId' => $academicYearId
            ]);
            // Return empty collection on error
        }

        return $branchUsers;
    }

    /**
     * Format staff users response
     */
    private function formatStaffUsersResponse(\Illuminate\Support\Collection $availableUsers, array $branchesData, string $overallAccessLevel, string $overallStaffRole, ?string $userType): array {
        // Group ALL users by user type for grouped_users array (no filtering)
        $groupedUsers = [];
        $totalCount = 0;

        foreach ($availableUsers as $user) {
            // For staff users, group by role if they have specific roles, otherwise by user_type
            if ($user->user_type === 'staff' && isset($user->role) && in_array($user->role, ['head_of_school', 'head_of_section'])) {
                $groupKey = $user->role;
            } else {
                $groupKey = $user->user_type;
            }

            // Create group if it doesn't exist
            if (!isset($groupedUsers[$groupKey])) {
                $groupedUsers[$groupKey] = [
                    'type' => $groupKey,
                    'type_label' => $this->getRoleLabel($groupKey),
                    'users' => [],
                    'count' => 0
                ];
            }

            // Add user to appropriate group
            $groupedUsers[$groupKey]['users'][] = [
                'id' => $user->id,
                'name' => $user->name,
                'user_type' => $user->user_type,
                'role' => $user->role ?? null,
                'email' => $user->email,
                'photo' => $user->photo ? "https://sis.bfi.edu.mm" . $user->photo : null,
                'branch_id' => $user->branch_id
            ];

            $groupedUsers[$groupKey]['count']++;
            $totalCount++;
        }

        // Sort groups by user type and role priority
        $userTypePriority = [
            'head_of_school' => 1,
            'head_of_section' => 2,
            'staff' => 3,
            'teacher' => 4,
            'parent' => 5,
            'student' => 6
        ];
        $sortedGroups = collect($groupedUsers)->sortBy(function($group) use ($userTypePriority) {
            return $userTypePriority[$group['type']] ?? 999;
        })->values()->toArray();

        // Apply user type filter ONLY for backward compatibility flat users array
        $filteredUsers = $availableUsers;
        if ($userType) {
            $filteredUsers = $availableUsers->where('user_type', $userType);
        }

        return [
            'grouped_users' => $sortedGroups, // Always contains all user types
            'total_count' => $totalCount, // Total count of all users
            'filtered_count' => $filteredUsers->count(), // Count after applying user_type filter
            'user_type' => 'staff',
            'staff_role' => $overallStaffRole,
            'access_level' => $overallAccessLevel,
            'branches' => $branchesData,
            'total_branches' => count($branchesData),
            'applied_filter' => $userType, // Show what filter was applied
            // Keep backward compatibility - this array respects user_type filter
            'users' => $filteredUsers->map(function($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'user_type' => $user->user_type,
                    'role' => $user->role ?? null,
                    'email' => $user->email,
                    'photo' => $user->photo ? "https://sis.bfi.edu.mm" . $user->photo : null,
                    'branch_id' => $user->branch_id
                ];
            })->values()->toArray()
        ];
    }

    /**
     * Add all users in branch (Head of School access)
     */
    private function addAllBranchUsers(&$availableUsers, $branchId, $staffId) {
        // Get active students with enhanced filtering
        $students = $this->getActiveStudentsInBranch($branchId, null);
        foreach ($students as $student) {
            $availableUsers->push((object)[
                'id' => $student->id,
                'name' => $student->name,
                'user_type' => $student->user_type,
                'role' => $student->user_type,
                'email' => $student->email,
                'photo' => $student->photo,
                'branch_id' => $student->branch_id
            ]);
        }

        // Get active staff members with role identification
        $staff = $this->getActiveStaffInBranch($branchId, $staffId, true);
        foreach ($staff as $staffMember) {
            $availableUsers->push((object)[
                'id' => $staffMember->id,
                'name' => $staffMember->name,
                'user_type' => $staffMember->user_type,
                'role' => $staffMember->role, // Use identified role instead of user_type
                'email' => $staffMember->email,
                'photo' => $staffMember->photo,
                'branch_id' => $staffMember->branch_id
            ]);
        }

        // Get other active users (parents, etc.) - excluding students and staff as we handled them above
        $otherUsers = User::where('user_status', 1)
            ->where('id', '!=', $staffId)
            ->where('branch_id', $branchId)
            ->whereNotIn('user_type', ['student', 'staff']) // Exclude students and staff as we handled them above
            ->select(['id', 'name', 'user_type', 'email', 'photo', 'branch_id'])
            ->get();

        foreach ($otherUsers as $user) {
            $availableUsers->push((object)[
                'id' => $user->id,
                'name' => $user->name,
                'user_type' => $user->user_type,
                'role' => $user->user_type,
                'email' => $user->email,
                'photo' => $user->photo,
                'branch_id' => $user->branch_id
            ]);
        }
    }

    /**
     * Get active students in branch with comprehensive filtering
     */
    private function getActiveStudentsInBranch(int $branchId, ?int $academicYearId): \Illuminate\Support\Collection {
        try {
            // Start with a simple query and add complexity gradually
            $students = User::where('user_status', 1)
                ->where('user_type', 'student')
                ->where('branch_id', $branchId)
                ->select(['id', 'name', 'user_type', 'email', 'photo', 'branch_id'])
                ->get();

            // If academic year is provided, filter by classroom enrollment
            if ($academicYearId) {
                $enrolledStudentIds = \DB::table('students_classroom')
                    ->where('branch_id', $branchId)
                    ->where('academic_year_id', $academicYearId)
                    ->pluck('student_id')
                    ->toArray();

                $students = $students->whereIn('id', $enrolledStudentIds);
            }

            return $students;
        } catch (\Exception $e) {
            \Log::error('Error in getActiveStudentsInBranch: ' . $e->getMessage(), [
                'branchId' => $branchId,
                'academicYearId' => $academicYearId
            ]);

            // Fallback to basic student query
            try {
                return User::where('user_status', 1)
                    ->where('user_type', 'student')
                    ->where('branch_id', $branchId)
                    ->select(['id', 'name', 'user_type', 'email', 'photo', 'branch_id'])
                    ->get();
            } catch (\Exception $fallbackError) {
                \Log::error('Fallback query also failed: ' . $fallbackError->getMessage());
                return collect(); // Return empty collection on complete failure
            }
        }
    }

    /**
     * Get active staff members in branch with comprehensive filtering and role identification
     */
    private function getActiveStaffInBranch(int $branchId, ?int $excludeStaffId = null, bool $identifyRoles = false): \Illuminate\Support\Collection {
        $query = User::where('user_status', 1) // Active user status
            ->where('user_type', 'staff')
            ->where('branch_id', $branchId);

        // Exclude specific staff member if provided (usually the current user)
        if ($excludeStaffId) {
            $query->where('id', '!=', $excludeStaffId);
        }

        $staff = $query->select(['id', 'name', 'user_type', 'email', 'photo', 'branch_id'])
                      ->get();

        // If role identification is requested, determine staff roles
        if ($identifyRoles) {
            return $this->identifyStaffRoles($staff, $branchId);
        }

        return $staff;
    }

    /**
     * Identify staff roles for proper grouping
     */
    private function identifyStaffRoles(\Illuminate\Support\Collection $staff, int $branchId): \Illuminate\Support\Collection {
        // Define role hierarchies
        $headOfSchoolRoles = [1, 2, 3]; // Adjust these role IDs based on your system
        $headOfSectionRoles = [52, 69, 70, 71, 72]; // Vice principal and department heads

        return $staff->map(function($staffMember) use ($branchId, $headOfSchoolRoles, $headOfSectionRoles) {
            // Default to staff role
            $staffMember->role = 'staff';

            try {
                // Get staff roles for this specific branch using direct DB query for better error handling
                $staffRoles = \DB::table('roles_user')
                    ->where('user_id', $staffMember->id)
                    ->where('branch_id', $branchId)
                    ->pluck('role_id')
                    ->toArray();

                // Determine staff role
                if (!empty($staffRoles)) {
                    if (array_intersect($staffRoles, $headOfSchoolRoles)) {
                        $staffMember->role = 'head_of_school';
                    } elseif (array_intersect($staffRoles, $headOfSectionRoles)) {
                        $staffMember->role = 'head_of_section';
                    }
                }
            } catch (\Exception $e) {
                // Log error but continue with default role
                \Log::error('Error identifying staff role: ' . $e->getMessage(), [
                    'staffId' => $staffMember->id ?? 'unknown',
                    'branchId' => $branchId
                ]);
            }

            return $staffMember;
        });
    }

    /**
     * Add section/department users (Head of Section access)
     */
    private function addSectionUsers(&$availableUsers, $branchId, $staffId, $academicYearId) {
        // Add all ACTIVE students in the branch (enhanced filtering)
        $students = $this->getActiveStudentsInBranch($branchId, $academicYearId);



        foreach ($students as $student) {
            $availableUsers->push((object)[
                'id' => $student->id,
                'name' => $student->name,
                'user_type' => $student->user_type,
                'role' => 'student',
                'email' => $student->email,
                'photo' => $student->photo,
                'branch_id' => $student->branch_id
            ]);
        }

        // Add all ACTIVE staff in the branch with role identification
        $staff = $this->getActiveStaffInBranch($branchId, $staffId, true);
        foreach ($staff as $staffMember) {
            $availableUsers->push((object)[
                'id' => $staffMember->id,
                'name' => $staffMember->name,
                'user_type' => $staffMember->user_type,
                'role' => $staffMember->role, // Use identified role instead of generic 'staff'
                'email' => $staffMember->email,
                'photo' => $staffMember->photo,
                'branch_id' => $staffMember->branch_id
            ]);
        }

        // Add parents of ACTIVE students in the section (enhanced filtering)
        $activeStudentIds = $students->pluck('id')->toArray();
        if (!empty($activeStudentIds)) {
            $parents = \DB::table('users_children')
                ->leftJoin('users as parents', 'parents.id', 'users_children.user_id')
                ->whereIn('users_children.child_id', $activeStudentIds)
                ->where('parents.user_status', 1)
                ->where('parents.user_type', 'parent')
                ->select(['parents.id', 'parents.name', 'parents.user_type', 'parents.email', 'parents.photo', 'parents.branch_id'])
                ->distinct()
                ->get();
        } else {
            $parents = collect();
        }



        foreach ($parents as $parent) {
            $availableUsers->push((object)[
                'id' => $parent->id,
                'name' => $parent->name,
                'user_type' => $parent->user_type,
                'role' => 'parent',
                'email' => $parent->email,
                'photo' => $parent->photo,
                'branch_id' => $parent->branch_id
            ]);
        }
    }

    /**
     * Add homeroom students and parents (Homeroom Teacher access)
     */
    private function addHomeroomUsers(&$availableUsers, $staffId, $branchId, $academicYearId) {
        // Get students in homeroom classes
        $students = \DB::table('classrooms')
            ->leftJoin('students_classroom', 'students_classroom.classroom_id', 'classrooms.classroom_id')
            ->leftJoin('users', 'users.id', 'students_classroom.student_id')
            ->where('classrooms.homeroom_teacher_id', $staffId)
            ->where('classrooms.academic_year_id', $academicYearId)
            ->where('classrooms.branch_id', $branchId)
            ->where('users.user_status', 1)
            ->select(['users.id', 'users.name', 'users.user_type', 'users.email', 'users.photo', 'users.branch_id'])
            ->distinct()
            ->get();

        foreach ($students as $student) {
            $availableUsers->push((object)[
                'id' => $student->id,
                'name' => $student->name,
                'user_type' => $student->user_type,
                'role' => 'homeroom_student',
                'email' => $student->email,
                'photo' => $student->photo,
                'branch_id' => $student->branch_id
            ]);
        }

        // Get parents of homeroom students
        $parents = \DB::table('classrooms')
            ->leftJoin('students_classroom', 'students_classroom.classroom_id', 'classrooms.classroom_id')
            ->leftJoin('users_children', 'users_children.child_id', 'students_classroom.student_id')
            ->leftJoin('users as parents', 'parents.id', 'users_children.user_id')
            ->where('classrooms.homeroom_teacher_id', $staffId)
            ->where('classrooms.academic_year_id', $academicYearId)
            ->where('classrooms.branch_id', $branchId)
            ->where('parents.user_status', 1)
            ->where('parents.user_type', 'parent')
            ->select(['parents.id', 'parents.name', 'parents.user_type', 'parents.email', 'parents.photo', 'parents.branch_id'])
            ->distinct()
            ->get();

        foreach ($parents as $parent) {
            $availableUsers->push((object)[
                'id' => $parent->id,
                'name' => $parent->name,
                'user_type' => $parent->user_type,
                'role' => 'homeroom_parent',
                'email' => $parent->email,
                'photo' => $parent->photo,
                'branch_id' => $parent->branch_id
            ]);
        }
    }

    /**
     * Add subject students (Subject Teacher access)
     */
    private function addSubjectTeacherUsers(&$availableUsers, $staffId, $branchId, $academicYearId) {
        // Get students who take subjects taught by this teacher
        $students = \DB::table('academic_timetable')
            ->leftJoin('academic_elective_grade', function($join) {
                $join->on('academic_elective_grade.grade_id', 'academic_timetable.grade_id')
                     ->on('academic_elective_grade.subject_id', 'academic_timetable.subject_id');
            })
            ->leftJoin('academic_elective_grade_students', 'academic_elective_grade_students.grade_id', 'academic_elective_grade.grade_id')
            ->leftJoin('users', 'users.id', 'academic_elective_grade_students.student_id')
            ->where('academic_timetable.user_id', $staffId)
            ->where('academic_timetable.branch_id', $branchId)
            ->where(function($query) use ($academicYearId) {
                $query->where('academic_timetable.academic_year_id', $academicYearId)
                      ->orWhereNull('academic_timetable.academic_year_id');
            })
            ->where('users.user_status', 1)
            ->where('users.user_type', 'student')
            ->select(['users.id', 'users.name', 'users.user_type', 'users.email', 'users.photo', 'users.branch_id'])
            ->distinct()
            ->get();

        // If no students found with academic year filter, try without it
        if ($students->isEmpty()) {
            $students = \DB::table('academic_timetable')
                ->leftJoin('academic_elective_grade', function($join) {
                    $join->on('academic_elective_grade.grade_id', 'academic_timetable.grade_id')
                         ->on('academic_elective_grade.subject_id', 'academic_timetable.subject_id');
                })
                ->leftJoin('academic_elective_grade_students', 'academic_elective_grade_students.grade_id', 'academic_elective_grade.grade_id')
                ->leftJoin('users', 'users.id', 'academic_elective_grade_students.student_id')
                ->where('academic_timetable.user_id', $staffId)
                ->where('academic_timetable.branch_id', $branchId)
                ->where('users.user_status', 1)
                ->where('users.user_type', 'student')
                ->select(['users.id', 'users.name', 'users.user_type', 'users.email', 'users.photo', 'users.branch_id'])
                ->distinct()
                ->get();
        }

        foreach ($students as $student) {
            $availableUsers->push((object)[
                'id' => $student->id,
                'name' => $student->name,
                'user_type' => $student->user_type,
                'role' => 'subject_student',
                'email' => $student->email,
                'photo' => $student->photo,
                'branch_id' => $student->branch_id
            ]);
        }

        // Also add parents of these students
        $studentIds = $students->pluck('id')->toArray();
        if (!empty($studentIds)) {
            $parents = \DB::table('users_children')
                ->leftJoin('users as parents', 'parents.id', 'users_children.user_id')
                ->whereIn('users_children.child_id', $studentIds)
                ->where('parents.user_status', 1)
                ->where('parents.user_type', 'parent')
                ->select(['parents.id', 'parents.name', 'parents.user_type', 'parents.email', 'parents.photo', 'parents.branch_id'])
                ->distinct()
                ->get();

            foreach ($parents as $parent) {
                $availableUsers->push((object)[
                    'id' => $parent->id,
                    'name' => $parent->name,
                    'user_type' => $parent->user_type,
                    'role' => 'subject_parent',
                    'email' => $parent->email,
                    'photo' => $parent->photo,
                    'branch_id' => $parent->branch_id
                ]);
            }
        }

        // Add other ACTIVE staff members in the same branch with role identification
        $staff = $this->getActiveStaffInBranch($branchId, $staffId, true);
        foreach ($staff as $staffMember) {
            $availableUsers->push((object)[
                'id' => $staffMember->id,
                'name' => $staffMember->name,
                'user_type' => $staffMember->user_type,
                'role' => $staffMember->role, // Use identified role instead of generic 'colleague'
                'email' => $staffMember->email,
                'photo' => $staffMember->photo,
                'branch_id' => $staffMember->branch_id
            ]);
        }
    }

    /**
     * Add limited users for general staff (only active staff members)
     */
    private function addGeneralStaffUsers(&$availableUsers, $branchId, $staffId) {
        // General staff can only message other ACTIVE staff members with role identification
        $staff = $this->getActiveStaffInBranch($branchId, $staffId, true);
        foreach ($staff as $staffMember) {
            $availableUsers->push((object)[
                'id' => $staffMember->id,
                'name' => $staffMember->name,
                'user_type' => $staffMember->user_type,
                'role' => $staffMember->role, // Use identified role instead of generic 'staff'
                'email' => $staffMember->email,
                'photo' => $staffMember->photo,
                'branch_id' => $staffMember->branch_id
            ]);
        }
    }

    /**
     * Helper method to get role labels
     */
    private function getRoleLabel($role) {
        $labels = [
            'head_of_school' => 'Head of School',
            'head_of_section' => 'Head of Section',
            'homeroom_teacher' => 'Homeroom Teacher',
            'subject_teacher' => 'Subject Teachers',
            'librarian' => 'Librarian',
            'classmate' => 'Classmates',
            'student' => 'Students',
            'staff' => 'Staff',
            'teacher' => 'Teachers',
            'parent' => 'Parents'
        ];

        return $labels[$role] ?? ucfirst($role);
    }

    /**
     * Get conversation members grouped by user type
     */
    public function getConversationMembers($authCode, $conversationUuid) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            $userId = $device->student_id;

            // Verify conversation exists and user is member
            $conversation = \App\Models\Chat::where('chat_uuid', $conversationUuid)->first();
            if (!$conversation) {
                return response()->json(['error' => 'Conversation not found'], 404);
            }

            $isMember = \App\Models\ChatUser::where('chat_id', $conversation->chat_id)
                ->where('user_id', $userId)
                ->exists();

            if (!$isMember) {
                return response()->json(['error' => 'Access denied to this conversation'], 403);
            }

            // Get conversation members
            $members = \App\Models\ChatUser::leftJoin('users', 'users.id', 'msg_chat_users.user_id')
                ->where('msg_chat_users.chat_id', $conversation->chat_id)
                ->select([
                    'users.id',
                    'users.name',
                    'users.user_type',
                    'users.email',
                    'users.photo',
                    'users.branch_id'
                ])
                ->get();

            // Group members by user type
            $groupedMembers = [];
            $totalCount = 0;

            foreach ($members as $member) {
                $userType = $member->user_type;

                if (!isset($groupedMembers[$userType])) {
                    $groupedMembers[$userType] = [
                        'type' => $userType,
                        'type_label' => ucfirst($userType),
                        'members' => [],
                        'count' => 0
                    ];
                }

                $groupedMembers[$userType]['members'][] = [
                    'id' => $member->id,
                    'name' => $member->name,
                    'user_type' => $member->user_type,
                    'email' => $member->email,
                    'photo' => $member->photo ? "https://sis.bfi.edu.mm" . $member->photo : null,
                    'branch_id' => $member->branch_id
                ];

                $groupedMembers[$userType]['count']++;
                $totalCount++;
            }

            // Sort grouped members by user type priority
            $userTypePriority = ['staff' => 1, 'teacher' => 2, 'parent' => 3, 'student' => 4];
            $sortedGroups = collect($groupedMembers)->sortBy(function($group) use ($userTypePriority) {
                return $userTypePriority[$group['type']] ?? 999;
            })->values()->toArray();

            return response()->json([
                'success' => true,
                'data' => [
                    'conversation' => [
                        'id' => $conversation->chat_id,
                        'uuid' => $conversation->chat_uuid,
                        'topic' => $conversation->chat_topic
                    ],
                    'grouped_members' => $sortedGroups,
                    'total_count' => $totalCount,
                    // Keep backward compatibility
                    'members' => $members->map(function($member) {
                        return [
                            'id' => $member->id,
                            'name' => $member->name,
                            'user_type' => $member->user_type,
                            'email' => $member->email,
                            'photo' => $member->photo ? "https://sis.bfi.edu.mm" . $member->photo : null,
                            'branch_id' => $member->branch_id
                        ];
                    })
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to retrieve conversation members'], 500);
        }
    }

    /**
     * Delete a conversation (only creator can delete)
     */
    public function deleteConversation($authCode, $conversationUuid) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            $userId = $device->student_id;

            // Find the conversation
            $conversation = \App\Models\Chat::where('chat_uuid', $conversationUuid)->first();
            if (!$conversation) {
                return response()->json(['error' => 'Conversation not found'], 404);
            }

            // Check if user is the creator of the conversation
            if ($conversation->created_by != $userId) {
                return response()->json(['error' => 'Only the conversation creator can delete it'], 403);
            }

            // Soft delete the conversation (set status to 0)
            $conversation->chat_status = 0;
            $conversation->save();

            // Notify all members about the deletion
            $members = \App\Models\ChatUser::where('chat_id', $conversation->chat_id)->get();
            $creator = \App\Models\User::find($userId);

            foreach ($members as $member) {
                if ($member->user_id != $userId) {
                    // Send notification to other members
                    $this->sendNotificationToUser(
                        $member->user_id,
                        'Conversation Deleted',
                        $creator->name . ' deleted the conversation "' . $conversation->chat_topic . '"',
                        'conversation_deleted',
                        [
                            'conversation_uuid' => $conversationUuid,
                            'deleted_by' => $creator->name,
                            'deleted_at' => now()->toISOString()
                        ]
                    );
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Conversation deleted successfully',
                'data' => [
                    'conversation_uuid' => $conversationUuid,
                    'deleted_by' => $creator->name,
                    'deleted_at' => now()->toISOString()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to delete conversation'], 500);
        }
    }

    /**
     * Leave a conversation (remove user from conversation)
     */
    public function leaveConversation($authCode, $conversationUuid) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            $userId = $device->student_id;

            // Find the conversation
            $conversation = \App\Models\Chat::where('chat_uuid', $conversationUuid)->first();
            if (!$conversation) {
                return response()->json(['error' => 'Conversation not found'], 404);
            }

            // Check if user is a member of the conversation
            $membership = \App\Models\ChatUser::where('chat_id', $conversation->chat_id)
                ->where('user_id', $userId)
                ->first();

            if (!$membership) {
                return response()->json(['error' => 'You are not a member of this conversation'], 403);
            }

            // Remove user from conversation
            $membership->delete();

            // Notify remaining members
            $remainingMembers = \App\Models\ChatUser::where('chat_id', $conversation->chat_id)->get();
            $leavingUser = \App\Models\User::find($userId);

            foreach ($remainingMembers as $member) {
                // Send notification to remaining members
                $this->sendNotificationToUser(
                    $member->user_id,
                    'User Left Conversation',
                    $leavingUser->name . ' left the conversation "' . $conversation->chat_topic . '"',
                    'user_left_conversation',
                    [
                        'conversation_uuid' => $conversationUuid,
                        'left_by' => $leavingUser->name,
                        'left_at' => now()->toISOString()
                    ]
                );
            }

            return response()->json([
                'success' => true,
                'message' => 'Successfully left the conversation',
                'data' => [
                    'conversation_uuid' => $conversationUuid,
                    'left_by' => $leavingUser->name,
                    'left_at' => now()->toISOString(),
                    'remaining_members_count' => count($remainingMembers)
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to leave conversation'], 500);
        }
    }

    /**
     * Delete a specific message (only sender can delete their own messages)
     */
    public function deleteMessage($authCode, $messageId) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            $userId = $device->student_id;

            // Find the message
            $message = \App\Models\ChatMessage::where('message_id', $messageId)->first();
            if (!$message) {
                return response()->json(['error' => 'Message not found'], 404);
            }

            // Check if user is the sender of the message
            if ($message->user_id != $userId) {
                return response()->json(['error' => 'You can only delete your own messages'], 403);
            }

            // Check if message is not too old (optional - you can set a time limit)
            $messageAge = now()->diffInHours($message->created_at);
            if ($messageAge > 24) { // 24 hours limit
                return response()->json(['error' => 'Cannot delete messages older than 24 hours'], 403);
            }

            // Soft delete the message (you might want to keep it for audit purposes)
            $message->delete();

            return response()->json([
                'success' => true,
                'message' => 'Message deleted successfully',
                'data' => [
                    'message_id' => $messageId,
                    'deleted_at' => now()->toISOString()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to delete message'], 500);
        }
    }

    /**
     * 1. Replace message text with "[Message Deleted]" (keep record but clear text)
     */
    public function clearMessageText($authCode, $messageId) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            $userId = $device->student_id;

            // Find the message
            $message = \App\Models\ChatMessage::where('message_id', $messageId)->first();
            if (!$message) {
                return response()->json(['error' => 'Message not found'], 404);
            }

            // Check if user is the sender of the message
            if ($message->user_id != $userId) {
                return response()->json(['error' => 'You can only clear your own messages'], 403);
            }

            // Check if message is not too old (24 hours limit)
            $messageAge = now()->diffInHours($message->created_at);
            if ($messageAge > 24) {
                return response()->json(['error' => 'Cannot clear messages older than 24 hours'], 403);
            }

            // Store original message for audit
            $originalMessage = $message->message;

            // Replace message text with "[Message Deleted]"
            $message->update([
                'message' => '[Message Deleted]',
                'message_type' => 'deleted',
                'original_message' => $originalMessage, // Store original for audit
                'deleted_at' => now(),
                'deleted_by' => $userId
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Message text cleared successfully',
                'data' => [
                    'message_id' => $messageId,
                    'new_content' => '[Message Deleted]',
                    'cleared_at' => now()->toISOString()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to clear message text'], 500);
        }
    }

    /**
     * 2. Admin delete - Allow admins/staff to delete any message
     */
    public function adminDeleteMessage($authCode, $messageId) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            $userId = $device->student_id;
            $user = \App\Models\User::find($userId);

            // Check if user is staff/admin
            if (!$user || $user->user_type !== 'staff') {
                return response()->json(['error' => 'Only staff members can perform admin delete'], 403);
            }

            // Find the message
            $message = \App\Models\ChatMessage::where('message_id', $messageId)->first();
            if (!$message) {
                return response()->json(['error' => 'Message not found'], 404);
            }

            // Get message sender info for logging
            $messageSender = \App\Models\User::find($message->user_id);

            // Store original message for audit
            $originalMessage = $message->message;

            // Admin can delete any message (no time limit)
            $message->update([
                'message' => '[Message Deleted by Admin]',
                'message_type' => 'admin_deleted',
                'original_message' => $originalMessage,
                'deleted_at' => now(),
                'deleted_by' => $userId,
                'admin_deleted' => true
            ]);

            // Log admin action
            \Log::warning('Admin deleted message', [
                'admin_id' => $userId,
                'admin_name' => $user->name,
                'message_id' => $messageId,
                'original_sender_id' => $message->user_id,
                'original_sender_name' => $messageSender ? $messageSender->name : 'Unknown',
                'original_message' => $originalMessage,
                'deleted_at' => now()->toISOString()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Message deleted by admin successfully',
                'data' => [
                    'message_id' => $messageId,
                    'new_content' => '[Message Deleted by Admin]',
                    'deleted_by_admin' => $user->name,
                    'original_sender' => $messageSender ? $messageSender->name : 'Unknown',
                    'deleted_at' => now()->toISOString()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to admin delete message'], 500);
        }
    }

    /**
     * 3. Bulk delete - Delete multiple messages at once
     */
    public function bulkDeleteMessages($authCode, $messageIds, $deleteType = 'soft') {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            $userId = $device->student_id;
            $user = \App\Models\User::find($userId);

            if (!is_array($messageIds) || empty($messageIds)) {
                return response()->json(['error' => 'message_ids must be a non-empty array'], 400);
            }

            $results = [];
            $successCount = 0;
            $errorCount = 0;

            \DB::beginTransaction();

            foreach ($messageIds as $messageId) {
                try {
                    $message = \App\Models\ChatMessage::where('message_id', $messageId)->first();

                    if (!$message) {
                        $results[] = [
                            'message_id' => $messageId,
                            'success' => false,
                            'error' => 'Message not found'
                        ];
                        $errorCount++;
                        continue;
                    }

                    // Check permissions
                    $canDelete = false;
                    $deleteReason = '';

                    if ($message->user_id == $userId) {
                        // User can delete their own messages (with time limit)
                        $messageAge = now()->diffInHours($message->created_at);
                        if ($messageAge <= 24) {
                            $canDelete = true;
                            $deleteReason = 'own_message';
                        } else {
                            $results[] = [
                                'message_id' => $messageId,
                                'success' => false,
                                'error' => 'Cannot delete messages older than 24 hours'
                            ];
                            $errorCount++;
                            continue;
                        }
                    } elseif ($user->user_type === 'staff') {
                        // Staff can delete any message
                        $canDelete = true;
                        $deleteReason = 'admin_delete';
                    }

                    if (!$canDelete) {
                        $results[] = [
                            'message_id' => $messageId,
                            'success' => false,
                            'error' => 'No permission to delete this message'
                        ];
                        $errorCount++;
                        continue;
                    }

                    // Perform deletion based on type
                    if ($deleteType === 'hard') {
                        $message->delete();
                        $newContent = null;
                    } else {
                        // Soft delete - replace with appropriate text
                        $originalMessage = $message->message;
                        $deletedText = $deleteReason === 'admin_delete' ? '[Message Deleted by Admin]' : '[Message Deleted]';

                        $message->update([
                            'message' => $deletedText,
                            'message_type' => $deleteReason === 'admin_delete' ? 'admin_deleted' : 'deleted',
                            'original_message' => $originalMessage,
                            'deleted_at' => now(),
                            'deleted_by' => $userId,
                            'admin_deleted' => $deleteReason === 'admin_delete'
                        ]);
                        $newContent = $deletedText;
                    }

                    $results[] = [
                        'message_id' => $messageId,
                        'success' => true,
                        'delete_type' => $deleteType,
                        'delete_reason' => $deleteReason,
                        'new_content' => $newContent
                    ];
                    $successCount++;

                } catch (\Exception $e) {
                    $results[] = [
                        'message_id' => $messageId,
                        'success' => false,
                        'error' => 'Failed to delete: ' . $e->getMessage()
                    ];
                    $errorCount++;
                }
            }

            \DB::commit();

            return response()->json([
                'success' => $successCount > 0,
                'message' => "Bulk delete completed: {$successCount} successful, {$errorCount} failed",
                'data' => [
                    'total_requested' => count($messageIds),
                    'successful_deletes' => $successCount,
                    'failed_deletes' => $errorCount,
                    'delete_type' => $deleteType,
                    'deleted_by' => $user->name,
                    'deleted_at' => now()->toISOString(),
                    'results' => $results
                ]
            ]);

        } catch (\Exception $e) {
            \DB::rollBack();
            return response()->json(['error' => 'Failed to bulk delete messages'], 500);
        }
    }

    /**
     * 4. Edit message - Modify message text (1-minute time limit)
     */
    public function editMessage($authCode, $messageId, $newContent) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            $userId = $device->student_id;

            // Find the message
            $message = \App\Models\ChatMessage::where('message_id', $messageId)->first();
            if (!$message) {
                return response()->json(['error' => 'Message not found'], 404);
            }

            // Check if user is the sender of the message
            if ($message->user_id != $userId) {
                return response()->json(['error' => 'You can only edit your own messages'], 403);
            }

            // Check if message is within 1-minute edit window
            $messageAge = now()->diffInMinutes($message->created_at);
            if ($messageAge > 1) {
                return response()->json([
                    'error' => 'Cannot edit messages older than 1 minute',
                    'message_age_minutes' => $messageAge,
                    'edit_deadline_passed' => true
                ], 403);
            }

            // Validate new content
            if (empty(trim($newContent))) {
                return response()->json(['error' => 'Message content cannot be empty'], 400);
            }

            if (strlen($newContent) > 1000) { // Assuming max message length
                return response()->json(['error' => 'Message content too long (max 1000 characters)'], 400);
            }

            // Store original message for audit
            $originalMessage = $message->message;

            // Update message content
            $message->update([
                'message' => $newContent,
                'original_message' => $originalMessage,
                'edited_at' => now(),
                'is_edited' => true
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Message edited successfully',
                'data' => [
                    'message_id' => $messageId,
                    'original_content' => $originalMessage,
                    'new_content' => $newContent,
                    'edited_at' => now()->toISOString(),
                    'edit_window_remaining_seconds' => max(0, 60 - ($messageAge * 60))
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to edit message'], 500);
        }
    }



    /**
     * Helper method to send notifications to users
     */
    private function sendNotificationToUser($userId, $title, $body, $type, $data = []) {
        try {
            // For now, just placeholder for notification
            // You can implement actual push notification logic here later

            // TODO: Implement actual push notification sending
            // This could use Firebase, APNs, or your existing notification system

        } catch (\Exception $e) {
            // Notification failed, but don't break the main flow
        }
    }

    // ========================================
    // GOOGLE DRIVE WORKSPACE MOBILE API METHODS
    // ========================================

    /**
     * Get workspace structure for mobile
     */
    public function getWorkspaceStructure($authCode) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            $branchId = $this->ah->mobileCurrentBranch($device->student_id);
            if (!$branchId) {
                return response()->json(['error' => 'Branch information not found'], 404);
            }

            $folders = \App\Models\GoogleDriveFolder::forBranch($branchId)
                ->active()
                ->with(['creator'])
                ->orderBy('folder_type')
                ->orderBy('folder_name')
                ->get();

            $rootFolder = $folders->where('folder_type', 'branch_root')->first();
            $subfolders = $folders->where('parent_folder_id', $rootFolder->folder_id ?? null);

            $workspaceData = [];

            if ($rootFolder) {
                $workspaceData['root_folder'] = [
                    'id' => $rootFolder->folder_id,
                    'name' => $rootFolder->folder_name,
                    'type' => $rootFolder->folder_type,
                    'description' => $rootFolder->description,
                    'icon' => $rootFolder->icon,
                    'color' => $rootFolder->color,
                    'web_link' => "https://drive.google.com/drive/folders/{$rootFolder->folder_id}",
                    'created_at' => $rootFolder->created_at->format('Y-m-d H:i:s'),
                    'creator_name' => $rootFolder->creator->name ?? 'System'
                ];

                $workspaceData['folders'] = [];
                foreach ($subfolders as $folder) {
                    $workspaceData['folders'][] = [
                        'id' => $folder->folder_id,
                        'name' => $folder->folder_name,
                        'type' => $folder->folder_type,
                        'description' => $folder->description,
                        'icon' => $folder->icon,
                        'color' => $folder->color,
                        'web_link' => "https://drive.google.com/drive/folders/{$folder->folder_id}",
                        'file_count' => $folder->files()->active()->count(),
                        'total_size' => $folder->formatted_total_size,
                        'created_at' => $folder->created_at->format('Y-m-d H:i:s'),
                        'creator_name' => $folder->creator->name ?? 'System'
                    ];
                }
            }

            return response()->json([
                'success' => true,
                'workspace' => $workspaceData,
                'branch_id' => $branchId,
                'user_type' => $device->user_type
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to get workspace structure: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Get folder contents for mobile
     */
    public function getFolderContents($authCode, $folderId) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            $branchId = $this->ah->mobileCurrentBranch($device->student_id);
            if (!$branchId) {
                return response()->json(['error' => 'Branch information not found'], 404);
            }

            // Get folder info
            $folder = \App\Models\GoogleDriveFolder::where('folder_id', $folderId)
                ->where('branch_id', $branchId)
                ->with(['creator'])
                ->first();

            if (!$folder) {
                return response()->json(['error' => 'Folder not found or access denied'], 404);
            }

            // Get subfolders
            $subfolders = \App\Models\GoogleDriveFolder::where('parent_folder_id', $folderId)
                ->where('branch_id', $branchId)
                ->active()
                ->with(['creator'])
                ->orderBy('folder_name')
                ->get();

            // Get files
            $files = \App\Models\GoogleDriveFile::where('folder_id', $folderId)
                ->where('branch_id', $branchId)
                ->active()
                ->with(['uploader'])
                ->orderBy('created_at', 'desc')
                ->get();

            $folderData = [];
            foreach ($subfolders as $subfolder) {
                $folderData[] = [
                    'id' => $subfolder->folder_id,
                    'name' => $subfolder->folder_name,
                    'type' => 'folder',
                    'folder_type' => $subfolder->folder_type,
                    'description' => $subfolder->description,
                    'icon' => $subfolder->icon,
                    'color' => $subfolder->color,
                    'web_link' => "https://drive.google.com/drive/folders/{$subfolder->folder_id}",
                    'file_count' => $subfolder->files()->active()->count(),
                    'created_at' => $subfolder->created_at->format('Y-m-d H:i:s'),
                    'creator_name' => $subfolder->creator->name ?? 'Unknown'
                ];
            }

            $fileData = [];
            foreach ($files as $file) {
                $fileData[] = [
                    'id' => $file->file_id,
                    'name' => $file->file_name,
                    'original_name' => $file->original_name,
                    'type' => 'file',
                    'file_type' => $file->file_type,
                    'mime_type' => $file->mime_type,
                    'size' => $file->file_size,
                    'formatted_size' => $file->formatted_size,
                    'description' => $file->description,
                    'web_view_link' => $file->web_view_link,
                    'web_content_link' => $file->web_content_link,
                    'is_google_file' => $file->is_google_file,
                    'is_shared' => $file->is_shared,
                    'created_at' => $file->created_at->format('Y-m-d H:i:s'),
                    'uploader_name' => $file->uploader->name ?? 'Unknown'
                ];
            }

            return response()->json([
                'success' => true,
                'folder_info' => [
                    'id' => $folder->folder_id,
                    'name' => $folder->folder_name,
                    'type' => $folder->folder_type,
                    'description' => $folder->description,
                    'path' => $folder->path,
                    'icon' => $folder->icon,
                    'color' => $folder->color,
                    'web_link' => "https://drive.google.com/drive/folders/{$folder->folder_id}",
                    'created_at' => $folder->created_at->format('Y-m-d H:i:s'),
                    'creator_name' => $folder->creator->name ?? 'Unknown'
                ],
                'folders' => $folderData,
                'files' => $fileData,
                'total_items' => count($folderData) + count($fileData)
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to get folder contents: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Upload file to Google Drive workspace via mobile
     */
    public function uploadWorkspaceFile($authCode, $file, $folderId, $description = null) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            if (!$file || !$file->isValid()) {
                return response()->json(['error' => 'Invalid file upload'], 400);
            }

            $branchId = $this->ah->mobileCurrentBranch($device->student_id);
            if (!$branchId) {
                return response()->json(['error' => 'Branch information not found'], 404);
            }

            // Validate folder access
            $folder = \App\Models\GoogleDriveFolder::where('folder_id', $folderId)
                ->where('branch_id', $branchId)
                ->first();

            if (!$folder) {
                return response()->json(['error' => 'Folder not found or access denied'], 404);
            }

            // File validation
            $allowedMimes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'jpeg', 'jpg', 'png', 'gif', 'mp4', 'mov', 'avi', 'mp3', 'wav', 'txt', 'csv'];
            $fileExtension = strtolower($file->getClientOriginalExtension());
            $maxSize = 100 * 1024 * 1024; // 100MB

            if (!in_array($fileExtension, $allowedMimes)) {
                return response()->json([
                    'error' => 'Invalid file type. Allowed types: ' . implode(', ', $allowedMimes)
                ], 400);
            }

            if ($file->getSize() > $maxSize) {
                return response()->json(['error' => 'File size exceeds 100MB limit'], 400);
            }

            // Use Google Drive Repository to upload
            $driveRepository = app(\App\Library\Repository\GoogleDriveRepository::class);
            $result = $driveRepository->uploadFile($file, $folderId, $description, true);

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => 'File uploaded successfully',
                    'file' => [
                        'id' => $result['file']['id'],
                        'name' => $result['file']['name'],
                        'size' => $result['file']['size'],
                        'mime_type' => $result['file']['mime_type'],
                        'web_view_link' => $result['file']['web_view_link'],
                        'web_content_link' => $result['file']['web_content_link'],
                        'created_time' => $result['file']['created_time']
                    ]
                ]);
            } else {
                return response()->json([
                    'error' => 'Failed to upload file: ' . $result['error']
                ], 500);
            }

        } catch (\Exception $e) {
            return response()->json(['error' => 'Upload failed: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Create folder in Google Drive workspace via mobile
     */
    public function createWorkspaceFolder($authCode, $folderName, $parentFolderId, $description = null) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            // Check if user is staff (only staff can create folders)
            if ($device->user_type !== 'staff') {
                return response()->json(['error' => 'Only staff members can create folders'], 403);
            }

            $branchId = $this->ah->mobileCurrentBranch($device->student_id);
            if (!$branchId) {
                return response()->json(['error' => 'Branch information not found'], 404);
            }

            // Validate parent folder access
            $parentFolder = \App\Models\GoogleDriveFolder::where('folder_id', $parentFolderId)
                ->where('branch_id', $branchId)
                ->first();

            if (!$parentFolder) {
                return response()->json(['error' => 'Parent folder not found or access denied'], 404);
            }

            // Validate folder name
            if (empty(trim($folderName))) {
                return response()->json(['error' => 'Folder name is required'], 400);
            }

            if (strlen($folderName) > 255) {
                return response()->json(['error' => 'Folder name too long (max 255 characters)'], 400);
            }

            // Use Google Drive Repository to create folder with correct created_by user ID
            $driveRepository = app(\App\Library\Repository\GoogleDriveRepository::class);
            $result = $driveRepository->createFolder($folderName, $parentFolderId, $description, 'custom', $device->student_id);

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => 'Folder created successfully',
                    'folder' => [
                        'id' => $result['folder']['id'],
                        'name' => $result['folder']['name'],
                        'web_view_link' => $result['folder']['web_view_link'],
                        'created_time' => $result['folder']['created_time']
                    ]
                ]);
            } else {
                return response()->json([
                    'error' => 'Failed to create folder: ' . $result['error']
                ], 500);
            }

        } catch (\Exception $e) {
            return response()->json(['error' => 'Folder creation failed: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Search files in workspace via mobile
     */
    public function searchWorkspaceFiles($authCode, $query) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            if (empty(trim($query))) {
                return response()->json(['error' => 'Search query is required'], 400);
            }

            $branchId = $this->ah->mobileCurrentBranch($device->student_id);
            if (!$branchId) {
                return response()->json(['error' => 'Branch information not found'], 404);
            }

            // Search in database first for faster results
            $files = \App\Models\GoogleDriveFile::where('branch_id', $branchId)
                ->active()
                ->where(function($q) use ($query) {
                    $q->where('file_name', 'like', "%{$query}%")
                      ->orWhere('original_name', 'like', "%{$query}%")
                      ->orWhere('description', 'like', "%{$query}%");
                })
                ->with(['uploader', 'folder'])
                ->orderBy('created_at', 'desc')
                ->limit(50)
                ->get();

            $searchResults = [];
            foreach ($files as $file) {
                $searchResults[] = [
                    'id' => $file->file_id,
                    'name' => $file->file_name,
                    'original_name' => $file->original_name,
                    'type' => 'file',
                    'file_type' => $file->file_type,
                    'mime_type' => $file->mime_type,
                    'size' => $file->file_size,
                    'formatted_size' => $file->formatted_size,
                    'description' => $file->description,
                    'web_view_link' => $file->web_view_link,
                    'web_content_link' => $file->web_content_link,
                    'is_google_file' => $file->is_google_file,
                    'folder_name' => $file->folder->folder_name ?? 'Unknown',
                    'folder_path' => $file->folder->path ?? '',
                    'created_at' => $file->created_at->format('Y-m-d H:i:s'),
                    'uploader_name' => $file->uploader->name ?? 'Unknown'
                ];
            }

            return response()->json([
                'success' => true,
                'query' => $query,
                'results' => $searchResults,
                'total_results' => count($searchResults)
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => 'Search failed: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Get recent files from workspace via mobile
     */
    public function getRecentWorkspaceFiles($authCode, $limit = 20) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            $branchId = $this->ah->mobileCurrentBranch($device->student_id);
            if (!$branchId) {
                return response()->json(['error' => 'Branch information not found'], 404);
            }

            $recentFiles = \App\Models\GoogleDriveFile::where('branch_id', $branchId)
                ->active()
                ->with(['uploader', 'folder'])
                ->orderBy('created_at', 'desc')
                ->limit($limit)
                ->get();

            $fileData = [];
            foreach ($recentFiles as $file) {
                $fileData[] = [
                    'id' => $file->file_id,
                    'name' => $file->file_name,
                    'original_name' => $file->original_name,
                    'type' => 'file',
                    'file_type' => $file->file_type,
                    'mime_type' => $file->mime_type,
                    'size' => $file->file_size,
                    'formatted_size' => $file->formatted_size,
                    'description' => $file->description,
                    'web_view_link' => $file->web_view_link,
                    'web_content_link' => $file->web_content_link,
                    'is_google_file' => $file->is_google_file,
                    'is_shared' => $file->is_shared,
                    'folder_name' => $file->folder->folder_name ?? 'Unknown',
                    'folder_id' => $file->folder_id,
                    'created_at' => $file->created_at->format('Y-m-d H:i:s'),
                    'uploader_name' => $file->uploader->name ?? 'Unknown',
                    'days_ago' => $file->created_at->diffInDays(now())
                ];
            }

            return response()->json([
                'success' => true,
                'recent_files' => $fileData,
                'total_files' => count($fileData),
                'limit' => $limit
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to get recent files: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Get workspace statistics via mobile
     */
    public function getWorkspaceStats($authCode) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            $branchId = $this->ah->mobileCurrentBranch($device->student_id);
            if (!$branchId) {
                return response()->json(['error' => 'Branch information not found'], 404);
            }

            $stats = [
                'total_files' => \App\Models\GoogleDriveFile::forBranch($branchId)->active()->count(),
                'total_folders' => \App\Models\GoogleDriveFolder::forBranch($branchId)->active()->count(),
                'total_size' => \App\Models\GoogleDriveFile::forBranch($branchId)->active()->sum('file_size'),
                'shared_files' => \App\Models\GoogleDriveFile::forBranch($branchId)->active()->shared()->count(),
                'recent_uploads' => \App\Models\GoogleDriveFile::forBranch($branchId)
                    ->active()
                    ->where('created_at', '>=', now()->subDays(7))
                    ->count(),
                'my_uploads' => \App\Models\GoogleDriveFile::forBranch($branchId)
                    ->active()
                    ->where('uploaded_by', $device->student_id)
                    ->count()
            ];

            // Format total size
            $bytes = $stats['total_size'];
            $units = ['B', 'KB', 'MB', 'GB', 'TB'];
            for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
                $bytes /= 1024;
            }
            $stats['formatted_total_size'] = round($bytes, 2) . ' ' . $units[$i];

            // Get file type distribution
            $fileTypes = \App\Models\GoogleDriveFile::forBranch($branchId)
                ->active()
                ->selectRaw('
                    CASE
                        WHEN mime_type LIKE "image/%" THEN "Images"
                        WHEN mime_type LIKE "video/%" THEN "Videos"
                        WHEN mime_type LIKE "audio/%" THEN "Audio"
                        WHEN mime_type = "application/pdf" THEN "PDF"
                        WHEN mime_type LIKE "application/vnd.google-apps.%" THEN "Google Docs"
                        WHEN mime_type LIKE "application/vnd.openxmlformats-%" THEN "Office"
                        WHEN mime_type LIKE "text/%" THEN "Text"
                        ELSE "Other"
                    END as file_category,
                    COUNT(*) as count
                ')
                ->groupBy('file_category')
                ->get()
                ->pluck('count', 'file_category')
                ->toArray();

            $stats['file_types'] = $fileTypes;

            return response()->json([
                'success' => true,
                'statistics' => $stats,
                'branch_id' => $branchId,
                'user_type' => $device->user_type
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to get workspace statistics: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Delete file or folder from workspace via mobile (staff only)
     */
    public function deleteWorkspaceItem($authCode, $itemId, $isFolder = false) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            // Check if user is staff (only staff can delete items)
            if ($device->user_type !== 'staff') {
                return response()->json(['error' => 'Only staff members can delete items'], 403);
            }

            $branchId = $this->ah->mobileCurrentBranch($device->student_id);
            if (!$branchId) {
                return response()->json(['error' => 'Branch information not found'], 404);
            }

            // Validate item access
            if ($isFolder) {
                $item = \App\Models\GoogleDriveFolder::where('folder_id', $itemId)
                    ->where('branch_id', $branchId)
                    ->first();

                if (!$item) {
                    return response()->json(['error' => 'Folder not found or access denied'], 404);
                }

                // Prevent deletion of system folders
                if ($item->is_system_folder) {
                    return response()->json(['error' => 'Cannot delete system folders'], 403);
                }
            } else {
                $item = \App\Models\GoogleDriveFile::where('file_id', $itemId)
                    ->where('branch_id', $branchId)
                    ->first();

                if (!$item) {
                    return response()->json(['error' => 'File not found or access denied'], 404);
                }
            }

            // Use Google Drive Repository to delete
            $driveRepository = app(\App\Library\Repository\GoogleDriveRepository::class);
            $result = $driveRepository->deleteItem($itemId, $isFolder);

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => ($isFolder ? 'Folder' : 'File') . ' deleted successfully'
                ]);
            } else {
                return response()->json([
                    'error' => 'Failed to delete item: ' . $result['error']
                ], 500);
            }

        } catch (\Exception $e) {
            return response()->json(['error' => 'Delete failed: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Search conversations and messages
     */
    public function searchMessages($authCode, $query, $type = 'all') {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            $userId = $device->student_id;
            $results = [];

            // Search in conversations
            if ($type === 'all' || $type === 'conversations') {
                $conversations = \App\Models\ChatUser::leftJoin('msg_chat', 'msg_chat.chat_id', 'msg_chat_users.chat_id')
                    ->where('msg_chat_users.user_id', $userId)
                    ->where('msg_chat.chat_status', 1)
                    ->where('msg_chat.chat_topic', 'LIKE', '%' . $query . '%')
                    ->select([
                        'msg_chat.chat_id',
                        'msg_chat.chat_uuid',
                        'msg_chat.chat_topic',
                        'msg_chat.created_at'
                    ])
                    ->get();

                $results['conversations'] = $conversations->map(function($conv) {
                    return [
                        'type' => 'conversation',
                        'id' => $conv->chat_id,
                        'uuid' => $conv->chat_uuid,
                        'title' => $conv->chat_topic,
                        'created_at' => $conv->created_at
                    ];
                });
            }

            // Search in messages
            if ($type === 'all' || $type === 'messages') {
                $messages = \App\Models\ChatMessage::leftJoin('msg_chat', 'msg_chat.chat_id', 'msg_messages.chat_id')
                    ->leftJoin('msg_chat_users', 'msg_chat_users.chat_id', 'msg_chat.chat_id')
                    ->leftJoin('users', 'users.id', 'msg_messages.user_id')
                    ->where('msg_chat_users.user_id', $userId)
                    ->where('msg_messages.message', 'LIKE', '%' . $query . '%')
                    ->select([
                        'msg_messages.message_id',
                        'msg_messages.message',
                        'msg_messages.created_at',
                        'msg_chat.chat_uuid',
                        'msg_chat.chat_topic',
                        'users.name as sender_name'
                    ])
                    ->orderBy('msg_messages.created_at', 'desc')
                    ->limit(50)
                    ->get();

                $results['messages'] = $messages->map(function($msg) {
                    return [
                        'type' => 'message',
                        'id' => $msg->message_id,
                        'content' => $msg->message,
                        'conversation_uuid' => $msg->chat_uuid,
                        'conversation_title' => $msg->chat_topic,
                        'sender_name' => $msg->sender_name,
                        'created_at' => $msg->created_at
                    ];
                });
            }

            return response()->json([
                'success' => true,
                'data' => $results,
                'query' => $query,
                'type' => $type
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to search messages'], 500);
        }
    }

    /**
     * Mark messages as read in a conversation
     */
    public function markMessagesAsRead($authCode, $conversationUuid) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            $userId = $device->student_id;

            // Verify conversation exists and user is member
            $conversation = \App\Models\Chat::where('chat_uuid', $conversationUuid)->first();
            if (!$conversation) {
                return response()->json(['error' => 'Conversation not found'], 404);
            }

            $isMember = \App\Models\ChatUser::where('chat_id', $conversation->chat_id)
                ->where('user_id', $userId)
                ->exists();

            if (!$isMember) {
                return response()->json(['error' => 'Access denied to this conversation'], 403);
            }

            // Update user's last read time for this conversation
            $chatUser = \App\Models\ChatUser::where('chat_id', $conversation->chat_id)
                ->where('user_id', $userId)
                ->first();

            if ($chatUser) {
                $chatUser->updated_at = now();
                $chatUser->save();
            }

            // Count how many messages were marked as read
            $unreadCount = \App\Models\ChatMessage::where('chat_id', $conversation->chat_id)
                ->where('user_id', '!=', $userId)
                ->where('created_at', '>', $chatUser->updated_at ?? now())
                ->count();

            return response()->json([
                'success' => true,
                'message' => 'Messages marked as read',
                'conversation_uuid' => $conversationUuid,
                'remaining_unread' => $unreadCount
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to mark messages as read'], 500);
        }
    }

    /**
     * Mark a specific message as read
     */
    public function markMessageAsRead($authCode, $messageId) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            $userId = $device->student_id;

            // Verify message exists and user has access to it
            $message = \App\Models\ChatMessage::leftJoin('msg_chat_users', 'msg_chat_users.chat_id', 'msg_messages.chat_id')
                ->where('msg_messages.message_id', $messageId)
                ->where('msg_chat_users.user_id', $userId)
                ->first();

            if (!$message) {
                return response()->json(['error' => 'Message not found or access denied'], 404);
            }

            // Don't mark own messages as read
            if ($message->user_id == $userId) {
                return response()->json([
                    'success' => true,
                    'message' => 'Cannot mark own message as read'
                ]);
            }

            // Update user's last read time for this conversation to include this message
            $chatUser = \App\Models\ChatUser::where('chat_id', $message->chat_id)
                ->where('user_id', $userId)
                ->first();

            if ($chatUser) {
                // Set the updated_at to the message's created_at time or later
                $messageTime = \App\Models\ChatMessage::where('message_id', $messageId)->value('created_at');
                if ($messageTime && (!$chatUser->updated_at || $messageTime > $chatUser->updated_at)) {
                    $chatUser->updated_at = $messageTime;
                    $chatUser->save();
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Message marked as read',
                'message_id' => $messageId
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to mark message as read'], 500);
        }
    }

    /**
     * Upload attachment for message
     */
    public function uploadMessageAttachment($authCode, $file) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            if (!$file || !$file->isValid()) {
                return response()->json(['error' => 'Invalid file upload'], 400);
            }

            // Validate file type and size
            $allowedTypes = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'txt'];
            $maxSize = 10 * 1024 * 1024; // 10MB

            $fileExtension = strtolower($file->getClientOriginalExtension());
            if (!in_array($fileExtension, $allowedTypes)) {
                return response()->json(['error' => 'File type not allowed'], 400);
            }

            if ($file->getSize() > $maxSize) {
                return response()->json(['error' => 'File size too large (max 10MB)'], 400);
            }

            // Generate unique filename
            $originalName = $file->getClientOriginalName();
            $safeName = time() . '_' . Str::random(10) . '.' . $fileExtension;

            // Create directory if it doesn't exist
            $filePath = 'uploads/messages/' . date('Y/m');
            $fullPath = $filePath . '/' . $safeName;

            if (!file_exists(public_path($filePath))) {
                mkdir(public_path($filePath), 0755, true);
            }

            // Move file to destination
            $file->move(public_path($filePath), $safeName);

            return response()->json([
                'success' => true,
                'message' => 'File uploaded successfully',
                'data' => [
                    'file_path' => $fullPath,
                    'file_name' => $originalName,
                    'file_size' => $file->getSize(),
                    'file_type' => $fileExtension,
                    'file_url' => url($fullPath)
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to upload attachment'], 500);
        }
    }

    // ========================================
    // HEALTH MANAGEMENT API METHODS
    // ========================================

    /**
     * Get student health records for mobile
     */
    public function getStudentHealthRecords($authCode) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            $studentId = $device->student_id;
            $user = User::find($studentId);

            if (!$user) {
                return response()->json(['error' => 'User not found'], 404);
            }

            // Get branch ID for mobile API context
            $branchId = $this->ah->mobileCurrentBranch($studentId);
            if (!$branchId) {
                return response()->json(['error' => 'Branch information not found'], 404);
            }

            $academicYearId = $this->ah->branchAcademicYear($branchId);

            // Check if academic year ID is valid
            if (!$academicYearId || !is_numeric($academicYearId)) {
                return response()->json(['error' => 'Academic year settings are invalid or not configured for this branch'], 500);
            }

            // Get student health records
            $records = HealthStudent::where('academic_year_id', $academicYearId)
                ->where('branch_id', $branchId)
                ->where('student_id', $studentId)
                ->where('status', 1)
                ->with(['student:id,name', 'user:id,name'])
                ->orderBy('health_record_id', 'desc')
                ->get();

            $formattedRecords = $records->map(function($record) {
                return [
                    'record_id' => $record->health_record_id,
                    'date' => $record->date,
                    'time' => $record->time,
                    'reason' => $record->reason,
                    'action' => $record->action,
                    'parent_contact_time' => $record->parent_contact_time,
                    'temperature' => $record->temperature,
                    'medication' => $record->medication,
                    'comments' => $record->comments,
                    'time_left_nurse_clinic' => $record->time_left_nurse_clinic,
                    'created_by' => $record->user ? $record->user->name : null,
                    'created_at' => $record->created_at,
                    'updated_at' => $record->updated_at
                ];
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'student' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'branch_id' => $branchId
                    ],
                    'records' => $formattedRecords,
                    'total_count' => $formattedRecords->count()
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Health API Error in getStudentHealthRecords', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'authCode' => substr($authCode, 0, 10) . '...'
            ]);

            // Check if it's an academic year related error
            if (strpos($e->getMessage(), 'academic_year_id') !== false ||
                strpos($e->getMessage(), 'Academic Year settings') !== false) {
                return response()->json(['error' => 'Academic year settings are invalid or not configured for this branch'], 500);
            }

            return response()->json(['error' => 'Failed to retrieve health records'], 500);
        }
    }

    /**
     * Get staff's own health records for mobile
     */
    public function getStaffHealthRecords($authCode) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            // Check if device is for staff
            if ($device->user_type !== 'staff') {
                return response()->json(['error' => 'Access denied. Staff authentication required'], 403);
            }

            $staffId = $device->student_id;
            $user = User::find($staffId);

            if (!$user) {
                return response()->json(['error' => 'User not found'], 404);
            }

            // Get branch ID for mobile API context
            $branchId = $this->ah->mobileCurrentBranch($staffId);
            if (!$branchId) {
                return response()->json(['error' => 'Branch information not found'], 404);
            }

            $academicYearId = $this->ah->branchAcademicYear($branchId);

            // Check if academic year ID is valid
            if (!$academicYearId || !is_numeric($academicYearId)) {
                return response()->json(['error' => 'Academic year settings are invalid or not configured for this branch'], 500);
            }

            // Get staff's own health records
            $records = HealthStaff::where('academic_year_id', $academicYearId)
                ->where('branch_id', $branchId)
                ->where('user_id', $staffId)
                ->where('status', 1)
                ->with(['user:id,name'])
                ->orderBy('record_id', 'desc')
                ->get();

            $formattedRecords = $records->map(function($record) {
                return [
                    'record_id' => $record->record_id,
                    'date' => $record->date,
                    'time' => $record->time,
                    'reason' => $record->reason,
                    'action' => $record->action,
                    'temperature' => $record->temperature,
                    'medication' => $record->medication,
                    'comments' => $record->comments,
                    'time_left_nurse_clinic' => $record->time_left_nurse_clinic,
                    'created_by' => $record->user ? $record->user->name : null,
                    'created_at' => $record->created_at,
                    'updated_at' => $record->updated_at
                ];
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'staff' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'branch_id' => $branchId
                    ],
                    'records' => $formattedRecords,
                    'total_count' => $formattedRecords->count()
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Health API Error in getStaffHealthRecords', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'authCode' => substr($authCode, 0, 10) . '...'
            ]);

            // Check if it's an academic year related error
            if (strpos($e->getMessage(), 'academic_year_id') !== false ||
                strpos($e->getMessage(), 'Academic Year settings') !== false) {
                return response()->json(['error' => 'Academic year settings are invalid or not configured for this branch'], 500);
            }

            return response()->json(['error' => 'Failed to retrieve health records'], 500);
        }
    }

    /**
     * Get homeroom students' health information for mobile (homeroom teachers only)
     */
    public function getHomeroomStudentsHealthInfo($authCode) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            // Check if device is for staff
            if ($device->user_type !== 'staff') {
                return response()->json(['error' => 'Access denied. Staff authentication required'], 403);
            }

            $teacherId = $device->student_id;

            // Get branch ID for mobile API context
            $branchId = $this->ah->mobileCurrentBranch($teacherId);
            if (!$branchId) {
                return response()->json(['error' => 'Branch information not found for teacher'], 404);
            }

            // Check if staff is homeroom teacher or nurse
            $accessLevel = $this->getStaffHealthAccessLevel($teacherId);
            if (!in_array($accessLevel, ['homeroom', 'nurse'])) {
                return response()->json(['error' => 'Access denied. Homeroom teacher or nurse access required'], 403);
            }

            $academicYearId = $this->ah->branchAcademicYear($branchId);

            // Check if academic year ID is valid
            if (!$academicYearId || !is_numeric($academicYearId)) {
                return response()->json(['error' => 'Academic year settings are invalid or not configured for this branch'], 500);
            }

            // Get homeroom students
            $homeroomStudentIds = \App\Models\StudentClassroom::leftJoin('classrooms', 'classrooms.classroom_id', 'student_classroom.classroom_id')
                ->where('classrooms.homeroom_teacher_id', $teacherId)
                ->where('classrooms.academic_year_id', $academicYearId)
                ->where('classrooms.branch_id', $branchId)
                ->pluck('student_classroom.student_id');

            if ($homeroomStudentIds->isEmpty()) {
                return response()->json([
                    'success' => true,
                    'data' => [
                        'teacher' => [
                            'id' => $teacherId,
                            'name' => User::find($teacherId)->name ?? 'Unknown',
                            'access_level' => $accessLevel
                        ],
                        'students_health_info' => [],
                        'total_count' => 0
                    ]
                ]);
            }

            // Get students with their health information and measurements
            $studentsHealthInfo = User::whereIn('id', $homeroomStudentIds)
                ->where('user_status', 1)
                ->with(['healthInformation', 'latestMeasurement'])
                ->select(['id', 'name', 'email'])
                ->get()
                ->map(function($student) use ($academicYearId, $branchId) {
                    $healthInfo = $student->healthInformation;

                    // Get latest measurement for current academic year
                    $latestMeasurement = StudentMeasurements::where('student_id', $student->id)
                        ->where('branch_id', $branchId)
                        ->where('academic_year_id', $academicYearId)
                        ->orderBy('date', 'desc')
                        ->first();

                    return [
                        'student_id' => $student->id,
                        'student_name' => $student->name,
                        'student_email' => $student->email,
                        'health_info' => $healthInfo ? [
                            'medical_conditions' => $healthInfo->medical_conditions,
                            'regularly_used_medication' => $healthInfo->regularly_used_medication,
                            'has_vision_problem' => $healthInfo->has_vision_problem,
                            'vision_check_date' => $healthInfo->vision_check_date,
                            'hearing_issue' => $healthInfo->hearing_issue,
                            'special_food_consideration' => $healthInfo->special_food_consideration,
                            'allergies' => $healthInfo->allergies,
                            'allergy_symtoms' => $healthInfo->allergy_symtoms,
                            'allergy_first_aid' => $healthInfo->allergy_first_aid,
                            'allowed_drugs' => $healthInfo->allowed_drugs,
                            'emergency_name_1' => $healthInfo->emergency_name_1,
                            'emergency_name_2' => $healthInfo->emergency_name_2,
                            'emergency_phone_1' => $healthInfo->emergency_phone_1,
                            'emergency_phone_2' => $healthInfo->emergency_phone_2
                        ] : null,
                        'measurements' => $latestMeasurement ? [
                            'height' => $latestMeasurement->height,
                            'weight' => $latestMeasurement->weight,
                            'date' => $latestMeasurement->date,
                            'has_measurements' => true
                        ] : [
                            'height' => null,
                            'weight' => null,
                            'date' => null,
                            'has_measurements' => false
                        ]
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => [
                    'teacher' => [
                        'id' => $teacherId,
                        'name' => User::find($teacherId)->name ?? 'Unknown',
                        'access_level' => $accessLevel,
                        'access_description' => $this->getAccessDescription($accessLevel)
                    ],
                    'students_health_info' => $studentsHealthInfo,
                    'total_count' => $studentsHealthInfo->count()
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Health API Error in getHomeroomStudentsHealthInfo', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'authCode' => substr($authCode, 0, 10) . '...'
            ]);

            // Check if it's an academic year related error
            if (strpos($e->getMessage(), 'academic_year_id') !== false ||
                strpos($e->getMessage(), 'Academic Year settings') !== false) {
                return response()->json(['error' => 'Academic year settings are invalid or not configured for this branch'], 500);
            }

            return response()->json(['error' => 'Failed to retrieve students health information'], 500);
        }
    }

    /**
     * Get student health information for mobile
     */
    public function getStudentHealthInfo($authCode) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            $studentId = $device->student_id;
            $user = User::find($studentId);

            if (!$user) {
                return response()->json(['error' => 'User not found'], 404);
            }

            // Get branch information for measurements query
            $branch = StudentInformation::where("id", $studentId)->first();
            if (!$branch) {
                return response()->json(['error' => 'Student information not found'], 404);
            }

            $academicYearId = $this->ah->branchAcademicYear($branch->branch_id);

            // Get student health information
            $healthInfo = StudentHealthInformation::find($studentId);

            // Get latest measurements for the current academic year
            $latestMeasurement = StudentMeasurements::where('student_id', $studentId)
                ->where('branch_id', $branch->branch_id)
                ->where('academic_year_id', $academicYearId)
                ->orderBy('date', 'desc')
                ->first();

            // Get all measurements for the current academic year (for history)
            $allMeasurements = StudentMeasurements::where('student_id', $studentId)
                ->where('branch_id', $branch->branch_id)
                ->where('academic_year_id', $academicYearId)
                ->orderBy('date', 'desc')
                ->get();

            $formattedHealthInfo = null;
            if ($healthInfo) {
                $formattedHealthInfo = [
                    'student_id' => $healthInfo->student_id,
                    'medical_conditions' => $healthInfo->medical_conditions,
                    'regularly_used_medication' => $healthInfo->regularly_used_medication,
                    'has_vision_problem' => $healthInfo->has_vision_problem,
                    'vision_check_date' => $healthInfo->vision_check_date,
                    'hearing_issue' => $healthInfo->hearing_issue,
                    'special_food_consideration' => $healthInfo->special_food_consideration,
                    'allergies' => $healthInfo->allergies,
                    'allergy_symtoms' => $healthInfo->allergy_symtoms,
                    'allergy_first_aid' => $healthInfo->allergy_first_aid,
                    'allowed_drugs' => $healthInfo->allowed_drugs,
                    'emergency_name_1' => $healthInfo->emergency_name_1,
                    'emergency_name_2' => $healthInfo->emergency_name_2,
                    'emergency_phone_1' => $healthInfo->emergency_phone_1,
                    'emergency_phone_2' => $healthInfo->emergency_phone_2
                ];
            }

            // Format measurements data
            $measurementsData = [
                'latest_measurement' => $latestMeasurement ? [
                    'id' => $latestMeasurement->id,
                    'height' => $latestMeasurement->height,
                    'weight' => $latestMeasurement->weight,
                    'date' => $latestMeasurement->date,
                    'created_at' => $latestMeasurement->created_at
                ] : null,
                'measurement_history' => $allMeasurements->map(function($measurement) {
                    return [
                        'id' => $measurement->id,
                        'height' => $measurement->height,
                        'weight' => $measurement->weight,
                        'date' => $measurement->date,
                        'created_at' => $measurement->created_at
                    ];
                }),
                'total_measurements' => $allMeasurements->count(),
                'has_measurements' => $allMeasurements->count() > 0
            ];

            return response()->json([
                'success' => true,
                'data' => [
                    'student' => [
                        'id' => $user->id,
                        'name' => $user->name
                    ],
                    'health_info' => $formattedHealthInfo,
                    'measurements' => $measurementsData
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Health API Error in getStudentHealthInfo', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'authCode' => substr($authCode, 0, 10) . '...'
            ]);
            return response()->json(['error' => 'Failed to retrieve health information'], 500);
        }
    }

    /**
     * Get teacher health data for mobile (staff only)
     */
    public function getTeacherHealthData($authCode) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            // Check if device is for staff
            if ($device->user_type !== 'staff') {
                return response()->json(['error' => 'Access denied. Staff authentication required'], 403);
            }

            $teacherId = $device->student_id; // This is actually staff ID for teacher devices

            // Get branch ID for mobile API context
            $branchId = $this->ah->mobileCurrentBranch($teacherId);
            if (!$branchId) {
                return response()->json(['error' => 'Branch information not found for teacher'], 404);
            }

            // Check staff health access level
            $accessLevel = $this->getStaffHealthAccessLevel($teacherId);
            if ($accessLevel === 'none') {
                return response()->json(['error' => 'Access denied. No health access permissions'], 403);
            }

            $academicYearId = $this->ah->branchAcademicYear($branchId);

            // Check if academic year ID is valid
            if (!$academicYearId || !is_numeric($academicYearId)) {
                return response()->json(['error' => 'Academic year settings are invalid or not configured for this branch'], 500);
            }

            // Initialize collections
            $studentRecords = collect();
            $staffRecords = collect();
            $guestRecords = collect();
            $students = collect();
            $staff = collect();

            if ($accessLevel === 'nurse') {
                // Nurses get full access to all records
                $studentRecords = HealthStudent::where('academic_year_id', $academicYearId)
                    ->where('branch_id', $branchId)
                    ->where('status', 1)
                    ->with(['student:id,name'])
                    ->orderBy('health_record_id', 'desc')
                    ->get();

                $staffRecords = HealthStaff::where('academic_year_id', $academicYearId)
                    ->where('branch_id', $branchId)
                    ->where('status', 1)
                    ->with(['user:id,name'])
                    ->orderBy('record_id', 'desc')
                    ->get();

                $guestRecords = HealthGuest::where('academic_year_id', $academicYearId)
                    ->where('branch_id', $branchId)
                    ->where('status', 1)
                    ->orderBy('health_record_id', 'desc')
                    ->get();

                // Get students and staff lists for creating new records
                $students = $this->ah->branchStudentsAY($branchId, $academicYearId);
                $staff = User::where('branch_id', $branchId)
                    ->where('user_type', 'staff')
                    ->where('user_status', 1)
                    ->select(['id', 'name', 'email'])
                    ->get();

            } elseif ($accessLevel === 'homeroom') {
                // Homeroom teachers get access to their students' records only
                $homeroomStudentIds = \App\Models\StudentClassroom::leftJoin('classrooms', 'classrooms.classroom_id', 'student_classroom.classroom_id')
                    ->where('classrooms.homeroom_teacher_id', $teacherId)
                    ->where('classrooms.academic_year_id', $academicYearId)
                    ->where('classrooms.branch_id', $branchId)
                    ->pluck('student_classroom.student_id');

                if ($homeroomStudentIds->isNotEmpty()) {
                    $studentRecords = HealthStudent::where('academic_year_id', $academicYearId)
                        ->where('branch_id', $branchId)
                        ->where('status', 1)
                        ->whereIn('student_id', $homeroomStudentIds)
                        ->with(['student:id,name'])
                        ->orderBy('health_record_id', 'desc')
                        ->get();

                    // Get homeroom students list (read-only)
                    $students = User::whereIn('id', $homeroomStudentIds)
                        ->where('user_status', 1)
                        ->select(['id', 'name', 'email'])
                        ->get();
                }

            } elseif ($accessLevel === 'staff') {
                // Regular staff get access to their own records only
                $staffRecords = HealthStaff::where('academic_year_id', $academicYearId)
                    ->where('branch_id', $branchId)
                    ->where('status', 1)
                    ->where('user_id', $teacherId)
                    ->with(['user:id,name'])
                    ->orderBy('record_id', 'desc')
                    ->get();
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'access_level' => $accessLevel,
                    'teacher' => [
                        'id' => $teacherId,
                        'name' => User::find($teacherId)->name ?? 'Unknown',
                        'branch_id' => $branchId,
                        'access_description' => $this->getAccessDescription($accessLevel)
                    ],
                    'student_records' => $studentRecords->map(function($record) {
                        return [
                            'record_id' => $record->health_record_id,
                            'student_name' => $record->student ? $record->student->name : 'Unknown',
                            'student_id' => $record->student_id,
                            'date' => $record->date,
                            'time' => $record->time,
                            'reason' => $record->reason,
                            'action' => $record->action,
                            'parent_contact_time' => $record->parent_contact_time,
                            'temperature' => $record->temperature,
                            'medication' => $record->medication,
                            'comments' => $record->comments,
                            'created_at' => $record->created_at
                        ];
                    }),
                    'staff_records' => $staffRecords->map(function($record) {
                        return [
                            'record_id' => $record->record_id,
                            'staff_name' => $record->user ? $record->user->name : 'Unknown',
                            'user_id' => $record->user_id,
                            'date' => $record->date,
                            'time' => $record->time,
                            'reason' => $record->reason,
                            'action' => $record->action,
                            'temperature' => $record->temperature,
                            'medication' => $record->medication,
                            'comments' => $record->comments,
                            'created_at' => $record->created_at
                        ];
                    }),
                    'guest_records' => $guestRecords->map(function($record) {
                        return [
                            'record_id' => $record->health_record_id,
                            'guest_name' => $record->full_name,
                            'date' => $record->date,
                            'time' => $record->time,
                            'reason' => $record->reason,
                            'action' => $record->action,
                            'temperature' => $record->temperature,
                            'medication' => $record->medication,
                            'comments' => $record->comments,
                            'created_at' => $record->created_at
                        ];
                    }),
                    'students' => $students->map(function($student) {
                        return [
                            'id' => $student->id,
                            'name' => $student->name
                        ];
                    }),
                    'staff' => $staff->map(function($staffMember) {
                        return [
                            'id' => $staffMember->id,
                            'name' => $staffMember->name
                        ];
                    }),
                    'counts' => [
                        'student_records' => $studentRecords->count(),
                        'staff_records' => $staffRecords->count(),
                        'guest_records' => $guestRecords->count()
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Health API Error in getTeacherHealthData', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'authCode' => substr($authCode, 0, 10) . '...'
            ]);

            // Check if it's an academic year related error
            if (strpos($e->getMessage(), 'academic_year_id') !== false ||
                strpos($e->getMessage(), 'Academic Year settings') !== false) {
                return response()->json(['error' => 'Academic year settings are invalid or not configured for this branch'], 500);
            }

            return response()->json(['error' => 'Failed to retrieve health data'], 500);
        }
    }

    /**
     * Create student health record for mobile (staff only)
     */
    public function createStudentHealthRecord($authCode, $data) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            // Check if device is for staff
            if ($device->user_type !== 'staff') {
                return response()->json(['error' => 'Access denied. Staff authentication required'], 403);
            }

            $teacherId = $device->student_id;

            // Check if staff has health module permissions (nurse access)
            $hasHealthPermission = $this->checkHealthPermission($teacherId);
            if (!$hasHealthPermission) {
                return response()->json(['error' => 'Access denied. Health module permission required (nurse access only)'], 403);
            }

            // Get branch ID for mobile API context
            $branchId = $this->ah->mobileCurrentBranch($teacherId);
            if (!$branchId) {
                return response()->json(['error' => 'Branch information not found for teacher'], 404);
            }

            $academicYearId = $this->ah->branchAcademicYear($branchId);

            // Check if academic year ID is valid
            if (!$academicYearId || !is_numeric($academicYearId)) {
                return response()->json(['error' => 'Academic year settings are invalid or not configured for this branch'], 500);
            }

            \DB::beginTransaction();

            $record = HealthStudent::create([
                'branch_id' => $branchId,
                'academic_year_id' => $academicYearId,
                'student_id' => $data['student_id'],
                'date' => $data['date'],
                'time' => $data['time'],
                'reason' => is_array($data['reason']) ? implode(',', $data['reason']) : $data['reason'],
                'action' => is_array($data['action']) ? implode(',', $data['action']) : $data['action'],
                'parent_contact_time' => $data['parent_contact_time'] ?? null,
                'temperature' => $data['temperature'] ?? null,
                'medication' => $data['medication'] ?? null,
                'comments' => $data['comments'] ?? null,
                'creator_id' => $teacherId,
                'status' => 1
            ]);

            \DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Student health record created successfully',
                'data' => [
                    'record_id' => $record->health_record_id,
                    'student_id' => $record->student_id,
                    'date' => $record->date,
                    'time' => $record->time,
                    'reason' => $record->reason,
                    'action' => $record->action,
                    'created_at' => $record->created_at
                ]
            ]);

        } catch (\Exception $e) {
            \DB::rollBack();
            return response()->json(['error' => 'Failed to create health record'], 500);
        }
    }

    /**
     * Create staff health record for mobile (staff only)
     */
    public function createStaffHealthRecord($authCode, $data) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            // Check if device is for staff
            if ($device->user_type !== 'staff') {
                return response()->json(['error' => 'Access denied. Staff authentication required'], 403);
            }

            $teacherId = $device->student_id;

            // Check if staff has health module permissions (nurse access)
            $hasHealthPermission = $this->checkHealthPermission($teacherId);
            if (!$hasHealthPermission) {
                return response()->json(['error' => 'Access denied. Health module permission required (nurse access only)'], 403);
            }

            // Get branch ID for mobile API context
            $branchId = $this->ah->mobileCurrentBranch($teacherId);
            if (!$branchId) {
                return response()->json(['error' => 'Branch information not found for teacher'], 404);
            }

            $academicYearId = $this->ah->branchAcademicYear($branchId);

            // Check if academic year ID is valid
            if (!$academicYearId || !is_numeric($academicYearId)) {
                return response()->json(['error' => 'Academic year settings are invalid or not configured for this branch'], 500);
            }

            \DB::beginTransaction();

            $record = HealthStaff::create([
                'branch_id' => $branchId,
                'academic_year_id' => $academicYearId,
                'user_id' => $data['user_id'],
                'date' => $data['date'],
                'time' => $data['time'],
                'reason' => is_array($data['reason']) ? implode(',', $data['reason']) : $data['reason'],
                'action' => is_array($data['action']) ? implode(',', $data['action']) : $data['action'],
                'temperature' => $data['temperature'] ?? null,
                'medication' => $data['medication'] ?? null,
                'comments' => $data['comments'] ?? null,
                'creator_id' => $teacherId,
                'status' => 1
            ]);

            \DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Staff health record created successfully',
                'data' => [
                    'record_id' => $record->record_id,
                    'user_id' => $record->user_id,
                    'date' => $record->date,
                    'time' => $record->time,
                    'reason' => $record->reason,
                    'action' => $record->action,
                    'created_at' => $record->created_at
                ]
            ]);

        } catch (\Exception $e) {
            \DB::rollBack();
            return response()->json(['error' => 'Failed to create health record'], 500);
        }
    }

    /**
     * Create guest health record for mobile (staff only)
     */
    public function createGuestHealthRecord($authCode, $data) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            // Check if device is for staff
            if ($device->user_type !== 'staff') {
                return response()->json(['error' => 'Access denied. Staff authentication required'], 403);
            }

            $teacherId = $device->student_id;

            // Check if staff has health module permissions (nurse access)
            $hasHealthPermission = $this->checkHealthPermission($teacherId);
            if (!$hasHealthPermission) {
                return response()->json(['error' => 'Access denied. Health module permission required (nurse access only)'], 403);
            }

            // Get branch ID for mobile API context
            $branchId = $this->ah->mobileCurrentBranch($teacherId);
            if (!$branchId) {
                return response()->json(['error' => 'Branch information not found for teacher'], 404);
            }

            $academicYearId = $this->ah->branchAcademicYear($branchId);

            // Check if academic year ID is valid
            if (!$academicYearId || !is_numeric($academicYearId)) {
                return response()->json(['error' => 'Academic year settings are invalid or not configured for this branch'], 500);
            }

            \DB::beginTransaction();

            $record = HealthGuest::create([
                'branch_id' => $branchId,
                'academic_year_id' => $academicYearId,
                'full_name' => $data['name'],
                'date' => $data['date'],
                'time' => $data['time'],
                'reason' => is_array($data['reason']) ? implode(',', $data['reason']) : $data['reason'],
                'action' => is_array($data['action']) ? implode(',', $data['action']) : $data['action'],
                'temperature' => $data['temperature'] ?? null,
                'medication' => $data['medication'] ?? null,
                'comments' => $data['comments'] ?? null,
                'creator_id' => $teacherId,
                'status' => 1
            ]);

            \DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Guest health record created successfully',
                'data' => [
                    'record_id' => $record->health_record_id,
                    'guest_name' => $record->full_name,
                    'date' => $record->date,
                    'time' => $record->time,
                    'reason' => $record->reason,
                    'action' => $record->action,
                    'created_at' => $record->created_at
                ]
            ]);

        } catch (\Exception $e) {
            \DB::rollBack();
            return response()->json(['error' => 'Failed to create health record'], 500);
        }
    }

    /**
     * Update student health information for mobile (staff only)
     */
    public function updateStudentHealthInfo($authCode, $data) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            // Check if device is for staff
            if ($device->user_type !== 'staff') {
                return response()->json(['error' => 'Access denied. Staff authentication required'], 403);
            }

            $teacherId = $device->student_id;

            // Check if staff has health module permissions (nurse access)
            $hasHealthPermission = $this->checkHealthPermission($teacherId);
            if (!$hasHealthPermission) {
                return response()->json(['error' => 'Access denied. Health module permission required (nurse access only)'], 403);
            }

            \DB::beginTransaction();

            $medications = '';
            if (isset($data['allowed_medication']) && is_array($data['allowed_medication'])) {
                foreach ($data['allowed_medication'] as $med) {
                    if ($med != '') {
                        $medications .= $med . ',';
                    }
                }
            }

            StudentHealthInformation::updateOrCreate(
                ['student_id' => $data['student_id']],
                [
                    'medical_conditions' => $data['medical_condition'] ?? null,
                    'regularly_used_medication' => $data['regularly_used_medication'] ?? null,
                    'has_vision_problem' => $data['vision_problem'] ?? null,
                    'vision_check_date' => $data['vision_check_date'] ?? null,
                    'hearing_issue' => $data['hearing_issue'] ?? null,
                    'special_food_consideration' => $data['food_consideration'] ?? null,
                    'allergies' => $data['allergy'] ?? null,
                    'allergy_symtoms' => $data['allergy_symtoms'] ?? null,
                    'allergy_first_aid' => $data['allergy_first_aid'] ?? null,
                    'allowed_drugs' => $medications,
                    'emergency_name_1' => $data['emergency_name_1'] ?? null,
                    'emergency_name_2' => $data['emergency_name_2'] ?? null,
                    'emergency_phone_1' => $data['emergency_phone_1'] ?? null,
                    'emergency_phone_2' => $data['emergency_phone_2'] ?? null
                ]
            );

            \DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Student health information updated successfully'
            ]);

        } catch (\Exception $e) {
            \DB::rollBack();
            return response()->json(['error' => 'Failed to update health information'], 500);
        }
    }

    /**
     * Delete health record for mobile (staff only)
     */
    public function deleteHealthRecord($authCode, $recordType, $recordId) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            // Check if device is for staff
            if ($device->user_type !== 'staff') {
                return response()->json(['error' => 'Access denied. Staff authentication required'], 403);
            }

            $teacherId = $device->student_id;

            // Check if staff has health module permissions (nurse access)
            $hasHealthPermission = $this->checkHealthPermission($teacherId);
            if (!$hasHealthPermission) {
                return response()->json(['error' => 'Access denied. Health module permission required (nurse access only)'], 403);
            }

            \DB::beginTransaction();

            switch ($recordType) {
                case 'student':
                    $record = HealthStudent::find($recordId);
                    if ($record) {
                        $record->status = 0;
                        $record->deleter_id = $teacherId;
                        $record->save();
                    }
                    break;

                case 'staff':
                    $record = HealthStaff::find($recordId);
                    if ($record) {
                        $record->status = 0;
                        $record->deleter_id = $teacherId;
                        $record->save();
                    }
                    break;

                case 'guest':
                    $record = HealthGuest::find($recordId);
                    if ($record) {
                        $record->status = 0;
                        $record->deleter_id = $teacherId;
                        $record->save();
                    }
                    break;

                default:
                    return response()->json(['error' => 'Invalid record type'], 400);
            }

            if (!$record) {
                return response()->json(['error' => 'Record not found'], 404);
            }

            \DB::commit();

            return response()->json([
                'success' => true,
                'message' => ucfirst($recordType) . ' health record deleted successfully'
            ]);

        } catch (\Exception $e) {
            \DB::rollBack();
            return response()->json(['error' => 'Failed to delete health record'], 500);
        }
    }

    /**
     * Get health lookup data for mobile (injuries, actions, medications)
     */
    public function getHealthLookupData($authCode) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            // Get lookup data from dictionary
            $injuries = Dictionary::where('type', 'health_injuries')->get();
            $actions = Dictionary::where('type', 'heath_actions')->get(); // Note: typo in original code
            $medications = Dictionary::where('type', 'health_drugs')->get();

            return response()->json([
                'success' => true,
                'data' => [
                    'injuries' => $injuries->map(function($item) {
                        return [
                            'id' => $item->id,
                            'value' => $item->value,
                            'description' => $item->description ?? null
                        ];
                    }),
                    'actions' => $actions->map(function($item) {
                        return [
                            'id' => $item->id,
                            'value' => $item->value,
                            'description' => $item->description ?? null
                        ];
                    }),
                    'medications' => $medications->map(function($item) {
                        return [
                            'id' => $item->id,
                            'value' => $item->value,
                            'description' => $item->description ?? null
                        ];
                    })
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to retrieve lookup data'], 500);
        }
    }

    /**
     * Check staff health access level
     * Returns: 'nurse' (full access), 'homeroom' (student read access), 'staff' (own records only), or 'none'
     */
    private function getStaffHealthAccessLevel($staffId) {
        try {
            // Health module ID is 42 (as seen in web routes)
            $healthModuleId = 42;

            // Get branch ID for the staff member
            $branchId = $this->ah->mobileCurrentBranch($staffId);
            if (!$branchId) {
                return 'none';
            }

            // Check if staff has full health module permissions (nurse access)
            $hasNursePermission = \App\Models\Permission::leftJoin('roles_user', 'roles_user.role_id', 'permissions.role_id')
                ->where('permissions.module_id', $healthModuleId)
                ->where('roles_user.user_id', $staffId)
                ->where('roles_user.branch_id', $branchId)
                ->where('permissions.p1', 1) // View permission
                ->count() > 0;

            if ($hasNursePermission) {
                return 'nurse';
            }

            // Check if staff is a homeroom teacher
            $academicYearId = $this->ah->branchAcademicYear($branchId);

            // Only check homeroom teacher status if academic year ID is valid
            $isHomeroomTeacher = false;
            if ($academicYearId && is_numeric($academicYearId)) {
                $isHomeroomTeacher = \App\Models\Classroom::where('homeroom_teacher_id', $staffId)
                    ->where('academic_year_id', $academicYearId)
                    ->where('branch_id', $branchId)
                    ->exists();
            }

            if ($isHomeroomTeacher) {
                return 'homeroom';
            }

            // Regular staff can access their own records
            return 'staff';
        } catch (\Exception $e) {
            \Log::error('Error checking health access level', [
                'staff_id' => $staffId,
                'error' => $e->getMessage()
            ]);
            return 'none';
        }
    }

    /**
     * Check if staff user has health module permissions (nurse access)
     */
    private function checkHealthPermission($staffId) {
        return $this->getStaffHealthAccessLevel($staffId) === 'nurse';
    }

    /**
     * Get access level description
     */
    private function getAccessDescription($accessLevel) {
        switch ($accessLevel) {
            case 'nurse':
                return 'Full access to all health records (nurse permissions)';
            case 'homeroom':
                return 'Read-only access to homeroom students\' health records';
            case 'staff':
                return 'Read-only access to own health records';
            default:
                return 'No health access';
        }
    }

    /**
     * Create student measurement record for mobile (staff only)
     */
    public function createStudentMeasurement($authCode, $data) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            if ($device->user_type !== 'staff') {
                return response()->json(['error' => 'Access denied. Staff authentication required'], 403);
            }

            $teacherId = $device->student_id;

            // Check if staff has health module permissions (nurse access)
            $hasHealthPermission = $this->checkHealthPermission($teacherId);
            if (!$hasHealthPermission) {
                return response()->json(['error' => 'Access denied. Health module permission required (nurse access only)'], 403);
            }

            // Validate required fields
            if (!isset($data['student_id']) || !isset($data['date']) || !isset($data['height']) || !isset($data['weight'])) {
                return response()->json(['error' => 'Student ID, date, height, and weight are required'], 400);
            }

            // Validate numeric values
            if (!is_numeric($data['height']) || !is_numeric($data['weight'])) {
                return response()->json(['error' => 'Height and weight must be numeric values'], 400);
            }

            // Validate ranges
            if ($data['height'] < 1 || $data['height'] > 3000) {
                return response()->json(['error' => 'Height must be between 1 and 3000 cm'], 400);
            }

            if ($data['weight'] < 1 || $data['weight'] > 200) {
                return response()->json(['error' => 'Weight must be between 1 and 200 kg'], 400);
            }

            // Get branch ID for mobile API context
            $branchId = $this->ah->mobileCurrentBranch($teacherId);
            if (!$branchId) {
                return response()->json(['error' => 'Branch information not found for teacher'], 404);
            }

            $academicYearId = $this->ah->branchAcademicYear($branchId);
            if (!$academicYearId || !is_numeric($academicYearId)) {
                return response()->json(['error' => 'Academic year settings are invalid or not configured for this branch'], 500);
            }

            \DB::beginTransaction();

            $measurement = StudentMeasurements::create([
                'branch_id' => $branchId,
                'academic_year_id' => $academicYearId,
                'student_id' => $data['student_id'],
                'date' => $data['date'],
                'height' => $data['height'],
                'weight' => $data['weight']
            ]);

            \DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Student measurement record created successfully',
                'data' => [
                    'measurement_id' => $measurement->id,
                    'student_id' => $measurement->student_id,
                    'date' => $measurement->date,
                    'height' => $measurement->height,
                    'weight' => $measurement->weight,
                    'created_at' => $measurement->created_at
                ]
            ]);

        } catch (\Exception $e) {
            \DB::rollBack();
            \Log::error('Health API Error in createStudentMeasurement', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'authCode' => substr($authCode, 0, 10) . '...'
            ]);
            return response()->json(['error' => 'Failed to create measurement record'], 500);
        }
    }

    /**
     * Delete student measurement record for mobile (staff only)
     */
    public function deleteStudentMeasurement($authCode, $measurementId) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            if ($device->user_type !== 'staff') {
                return response()->json(['error' => 'Access denied. Staff authentication required'], 403);
            }

            $teacherId = $device->student_id;

            // Check if staff has health module permissions (nurse access)
            $hasHealthPermission = $this->checkHealthPermission($teacherId);
            if (!$hasHealthPermission) {
                return response()->json(['error' => 'Access denied. Health module permission required (nurse access only)'], 403);
            }

            \DB::beginTransaction();

            $measurement = StudentMeasurements::find($measurementId);
            if (!$measurement) {
                return response()->json(['error' => 'Measurement record not found'], 404);
            }

            $measurement->delete();

            \DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Student measurement record deleted successfully'
            ]);

        } catch (\Exception $e) {
            \DB::rollBack();
            \Log::error('Health API Error in deleteStudentMeasurement', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'authCode' => substr($authCode, 0, 10) . '...'
            ]);
            return response()->json(['error' => 'Failed to delete measurement record'], 500);
        }
    }

    /**
     * Get calendar events for mobile API with branch-based filtering
     */
    public function getCalendarData($authCode, $startDate = null, $endDate = null) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            // Get user information
            $user = User::find($device->student_id);
            if (!$user) {
                return response()->json(['error' => 'User not found'], 404);
            }

            // Get user's branches
            $userBranches = [];
            if ($device->user_type === 'staff') {
                $userBranches = \DB::table('users_branches')
                    ->leftJoin('branches', 'branches.branch_id', 'users_branches.branch_id')
                    ->where('user_id', $device->student_id)
                    ->get();
            } else {
                // For students, get their branch
                $studentInfo = StudentInformation::where('id', $device->student_id)->first();
                if ($studentInfo) {
                    $branch = \DB::table('branches')->where('branch_id', $studentInfo->branch_id)->first();
                    if ($branch) {
                        $userBranches = collect([$branch]);
                    }
                }
            }

            if (empty($userBranches) || $userBranches->isEmpty()) {
                return response()->json(['error' => 'No branches found for user'], 404);
            }

            $branchesData = [];

            foreach ($userBranches as $branch) {
                $branchId = $branch->branch_id;
                $academicYearId = $this->ah->branchAcademicYear($branchId);

                $branchCalendarData = $this->getBranchCalendarData($branchId, $academicYearId, $startDate, $endDate);

                $branchesData[] = [
                    'branch_id' => $branchId,
                    'branch_name' => $branch->branch_name,
                    'branch_description' => $branch->branch_description ?? null,
                    'academic_year_id' => $academicYearId,
                    'calendar_data' => $branchCalendarData
                ];
            }

            return response()->json([
                'success' => true,
                'user_id' => $device->student_id,
                'user_type' => $device->user_type,
                'branches' => $branchesData,
                'total_branches' => count($branchesData),
                'date_range' => [
                    'start_date' => $startDate,
                    'end_date' => $endDate
                ],
                'generated_at' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            \Log::error('Calendar API error', [
                'auth_code' => $authCode,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['error' => 'Failed to retrieve calendar data'], 500);
        }
    }

    /**
     * Get calendar data for a specific branch
     */
    private function getBranchCalendarData($branchId, $academicYearId, $startDate = null, $endDate = null) {
        $calendarData = [
            'google_calendar_events' => [],
            'local_global_events' => [],
            'academic_calendar_events' => [],
            'mobile_calendar_image' => null,
            'summary' => [
                'total_events' => 0,
                'google_events_count' => 0,
                'local_events_count' => 0,
                'academic_events_count' => 0
            ]
        ];

        // Get Google Calendar events
        $googleEvents = $this->getGoogleCalendarEvents($branchId, $academicYearId, $startDate, $endDate);
        $calendarData['google_calendar_events'] = $googleEvents;
        $calendarData['summary']['google_events_count'] = count($googleEvents);

        // Get local global calendar events
        $localEvents = $this->getLocalCalendarEvents($startDate, $endDate);
        $calendarData['local_global_events'] = $localEvents;
        $calendarData['summary']['local_events_count'] = count($localEvents);

        // Get academic calendar events
        $academicEvents = $this->getAcademicCalendarEvents($academicYearId, $startDate, $endDate);
        $calendarData['academic_calendar_events'] = $academicEvents;
        $calendarData['summary']['academic_events_count'] = count($academicEvents);

        // Get mobile calendar image (existing functionality)
        $mobileCalendar = \App\Models\MobileBranchCalendar::where('branch_id', $branchId)
            ->where('academic_year_id', $academicYearId)
            ->where('calendar_status', 1)
            ->first();

        if ($mobileCalendar) {
            $calendarData['mobile_calendar_image'] = $mobileCalendar->calendar_file;
        }

        $calendarData['summary']['total_events'] =
            $calendarData['summary']['google_events_count'] +
            $calendarData['summary']['local_events_count'] +
            $calendarData['summary']['academic_events_count'];

        return $calendarData;
    }

    /**
     * Get Google Calendar events for a branch
     */
    private function getGoogleCalendarEvents($branchId, $academicYearId, $startDate = null, $endDate = null) {
        try {
            // Get Google Calendar configurations for the branch
            $googleCalendars = BranchGoogleCalendar::getActiveCalendarsForBranch($branchId, $academicYearId);

            if ($googleCalendars->isEmpty()) {
                return [];
            }

            $googleCalendarService = new GoogleCalendarService();
            $allEvents = [];

            foreach ($googleCalendars as $calendarConfig) {
                try {
                    $events = [];

                    if ($startDate && $endDate) {
                        $events = $googleCalendarService->getEventsByDateRange(
                            $calendarConfig->calendar_id,
                            $startDate,
                            $endDate
                        );
                    } else {
                        $events = $googleCalendarService->getCalendarEvents($calendarConfig->calendar_id);
                    }

                    // Add calendar source information to each event
                    foreach ($events as &$event) {
                        $event['calendar_source'] = [
                            'config_id' => $calendarConfig->calendar_config_id,
                            'calendar_name' => $calendarConfig->calendar_name,
                            'is_primary' => $calendarConfig->is_primary,
                            'timezone' => $calendarConfig->calendar_timezone
                        ];
                    }

                    $allEvents = array_merge($allEvents, $events);

                    // Update last synced timestamp
                    $calendarConfig->updateLastSynced();

                } catch (\Exception $e) {
                    \Log::error('Google Calendar sync error', [
                        'calendar_id' => $calendarConfig->calendar_id,
                        'branch_id' => $branchId,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            // Sort events by start date
            usort($allEvents, function($a, $b) {
                return strtotime($a['start_date']) - strtotime($b['start_date']);
            });

            return $allEvents;

        } catch (\Exception $e) {
            \Log::error('Google Calendar events retrieval error', [
                'branch_id' => $branchId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Get local calendar events (CalendarGlobal)
     */
    private function getLocalCalendarEvents($startDate = null, $endDate = null) {
        try {
            $query = CalendarGlobal::query();

            if ($startDate && $endDate) {
                $query->where(function($q) use ($startDate, $endDate) {
                    $q->whereBetween('start_date', [$startDate, $endDate])
                      ->orWhereBetween('end_date', [$startDate, $endDate])
                      ->orWhere(function($subQ) use ($startDate, $endDate) {
                          $subQ->where('start_date', '<=', $startDate)
                               ->where('end_date', '>=', $endDate);
                      });
                });
            }

            $events = $query->orderBy('start_date', 'asc')->get();

            return $events->map(function($event) {
                return [
                    'id' => $event->event_id,
                    'title' => $event->title ?? 'No Title',
                    'description' => $event->description ?? '',
                    'start_date' => $event->start_date,
                    'end_date' => $event->end_date,
                    'start_time' => null, // Local events don't have specific times
                    'end_time' => null,
                    'is_all_day' => true,
                    'location' => $event->location ?? '',
                    'status' => 'confirmed',
                    'created_at' => $event->created_at ? $event->created_at->format('Y-m-d') : null,
                    'updated_at' => $event->updated_at ? $event->updated_at->format('Y-m-d') : null,
                    'source' => 'local_global'
                ];
            })->toArray();

        } catch (\Exception $e) {
            \Log::error('Local calendar events retrieval error', [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Get academic calendar events
     */
    private function getAcademicCalendarEvents($academicYearId, $startDate = null, $endDate = null) {
        try {
            $query = AcademicCalendar::where('academic_year_id', $academicYearId);

            if ($startDate && $endDate) {
                $query->whereBetween('date', [$startDate, $endDate]);
            }

            $events = $query->orderBy('date', 'asc')->get();

            return $events->map(function($event) {
                return [
                    'id' => $event->academic_calendar_id,
                    'title' => $event->description ?? 'Academic Event',
                    'description' => $event->description ?? '',
                    'start_date' => $event->date,
                    'end_date' => $event->date, // Academic events are typically single-day
                    'start_time' => null,
                    'end_time' => null,
                    'is_all_day' => true,
                    'location' => '',
                    'status' => 'confirmed',
                    'created_at' => $event->created_at ? $event->created_at->format('Y-m-d') : null,
                    'updated_at' => $event->updated_at ? $event->updated_at->format('Y-m-d') : null,
                    'source' => 'academic_calendar',
                    'academic_year_id' => $event->academic_year_id
                ];
            })->toArray();

        } catch (\Exception $e) {
            \Log::error('Academic calendar events retrieval error', [
                'academic_year_id' => $academicYearId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Get upcoming calendar events (next 30 days)
     */
    public function getUpcomingCalendarEvents($authCode, $days = 30) {
        $startDate = now()->format('Y-m-d');
        $endDate = now()->addDays($days)->format('Y-m-d');

        return $this->getCalendarData($authCode, $startDate, $endDate);
    }

    /**
     * Get calendar events for a specific month
     */
    public function getMonthlyCalendarEvents($authCode, $year = null, $month = null) {
        $year = $year ?: now()->year;
        $month = $month ?: now()->month;

        $startDate = \Carbon\Carbon::create($year, $month, 1)->format('Y-m-d');
        $endDate = \Carbon\Carbon::create($year, $month, 1)->endOfMonth()->format('Y-m-d');

        return $this->getCalendarData($authCode, $startDate, $endDate);
    }

    /**
     * Test Google Calendar connection for a branch
     */
    public function testGoogleCalendarConnection($authCode, $branchId) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            // Check if device is for staff (only staff can test connections)
            if ($device->user_type !== 'staff') {
                return response()->json(['error' => 'Access denied. Staff authentication required'], 403);
            }

            $academicYearId = $this->ah->branchAcademicYear($branchId);
            $googleCalendars = BranchGoogleCalendar::getActiveCalendarsForBranch($branchId, $academicYearId);

            if ($googleCalendars->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'No Google Calendar configurations found for this branch',
                    'data' => [
                        'branch_id' => $branchId,
                        'academic_year_id' => $academicYearId,
                        'configured_calendars' => 0
                    ]
                ]);
            }

            $googleCalendarService = new GoogleCalendarService();
            $testResults = [];

            foreach ($googleCalendars as $calendarConfig) {
                $isAccessible = $googleCalendarService->testCalendarAccess($calendarConfig->calendar_id);
                $calendarInfo = null;

                if ($isAccessible) {
                    $calendarInfo = $googleCalendarService->getCalendarInfo($calendarConfig->calendar_id);
                }

                $testResults[] = [
                    'config_id' => $calendarConfig->calendar_config_id,
                    'calendar_id' => $calendarConfig->calendar_id,
                    'calendar_name' => $calendarConfig->calendar_name,
                    'is_primary' => $calendarConfig->is_primary,
                    'is_accessible' => $isAccessible,
                    'calendar_info' => $calendarInfo,
                    'last_synced_at' => $calendarConfig->last_synced_at
                ];
            }

            $successfulConnections = collect($testResults)->where('is_accessible', true)->count();

            return response()->json([
                'success' => true,
                'message' => "Tested {$googleCalendars->count()} calendar configurations",
                'data' => [
                    'branch_id' => $branchId,
                    'academic_year_id' => $academicYearId,
                    'total_calendars' => $googleCalendars->count(),
                    'successful_connections' => $successfulConnections,
                    'failed_connections' => $googleCalendars->count() - $successfulConnections,
                    'test_results' => $testResults,
                    'tested_at' => now()->toISOString()
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Google Calendar connection test error', [
                'branch_id' => $branchId,
                'error' => $e->getMessage()
            ]);
            return response()->json(['error' => 'Failed to test Google Calendar connection'], 500);
        }
    }

    /**
     * Get About Us information for mobile app
     */
    public function getAboutUsData() {
        try {
            $aboutInformations = MobileAboutInfo::with('branch')->get();

            $formattedData = [];

            foreach ($aboutInformations as $aboutInfo) {
                // Parse headers and bodies
                $headers = explode('|*|', $aboutInfo->about_info_headers);
                $bodies = explode('|*|', $aboutInfo->about_info_bodies);

                $sections = [];
                foreach ($headers as $index => $header) {
                    if (!empty($header) && isset($bodies[$index])) {
                        $sections[] = [
                            'header' => $header,
                            'body' => $bodies[$index] ?? ''
                        ];
                    }
                }

                $formattedData[] = [
                    'about_info_id' => $aboutInfo->about_info_id,
                    'branch_id' => $aboutInfo->branch_id,
                    'branch_name' => $aboutInfo->branch->branch_name ?? '',
                    'branch_logo' => $aboutInfo->branch_logo ? "https://sis.bfi.edu.mm" . $aboutInfo->branch_logo : null,
                    'sections' => $sections
                ];
            }

            return response()->json([
                'success' => true,
                'about_information' => $formattedData,
                'total_branches' => count($formattedData),
                'generated_at' => now()->toISOString()
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get about us data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get Contacts information for mobile app
     */
    public function getContactsData() {
        try {
            $aboutInformations = MobileAboutInfo::with('branch')->get();

            $formattedData = [];

            foreach ($aboutInformations as $aboutInfo) {
                $formattedData[] = [
                    'branch_id' => $aboutInfo->branch_id,
                    'branch_name' => $aboutInfo->branch->branch_name ?? '',
                    'branch_email' => $aboutInfo->branch->branch_email ?? '',
                    'branch_address' => $aboutInfo->branch->branch_address ?? '',
                    'branch_website' => $aboutInfo->branch->branch_website ?? '',
                    'branch_phone' => $aboutInfo->branch->branch_phone ?? '',
                    'branch_logo' => $aboutInfo->branch_logo ? "https://sis.bfi.edu.mm" . $aboutInfo->branch_logo : null
                ];
            }

            return response()->json([
                'success' => true,
                'contacts' => $formattedData,
                'total_branches' => count($formattedData),
                'generated_at' => now()->toISOString()
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get contacts data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get FAQ information for mobile app
     */
    public function getFAQData() {
        try {
            $branches = Branch::where('branch_status', 1)
                ->where('branch_id', '>', 4)
                ->with(['faqs' => function($query) {
                    $query->orderBy('faq_id', 'asc');
                }])
                ->get();

            $formattedData = [];

            foreach ($branches as $branch) {
                $faqs = [];
                foreach ($branch->faqs as $faq) {
                    $faqs[] = [
                        'faq_id' => $faq->faq_id,
                        'question' => $faq->faq_question,
                        'answer' => $faq->faq_answer
                    ];
                }

                $formattedData[] = [
                    'branch_id' => $branch->branch_id,
                    'branch_name' => $branch->branch_name,
                    'faqs' => $faqs,
                    'total_faqs' => count($faqs)
                ];
            }

            return response()->json([
                'success' => true,
                'faq_data' => $formattedData,
                'total_branches' => count($formattedData),
                'generated_at' => now()->toISOString()
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get FAQ data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get personalized calendar events for a user
     */
    public function getPersonalCalendarEvents($authCode, $startDate = null, $endDate = null) {
        try {
            $device = MobileDevice::where('auth_code', $authCode)->first();

            if (!$device) {
                return response()->json(['error' => 'Invalid authentication code'], 401);
            }

            $user = User::find($device->student_id);
            if (!$user) {
                return response()->json(['error' => 'User not found'], 404);
            }

            $personalEvents = [];

            // Get homework due dates
            $homeworkEvents = $this->getHomeworkDueDatesForUser($device->student_id, $device->user_type, $startDate, $endDate);
            $personalEvents = array_merge($personalEvents, $homeworkEvents);

            // Get exam/assessment schedules
            $examEvents = $this->getExamScheduleForUser($device->student_id, $device->user_type, $startDate, $endDate);
            $personalEvents = array_merge($personalEvents, $examEvents);

            // Get student birthdays (for homeroom teachers)
            if ($device->user_type === 'staff') {
                $birthdayEvents = $this->getStudentBirthdaysForTeacher($device->student_id, $startDate, $endDate);
                $personalEvents = array_merge($personalEvents, $birthdayEvents);
            }

            // Sort events by date
            usort($personalEvents, function($a, $b) {
                return strtotime($a['start_date']) - strtotime($b['start_date']);
            });

            return response()->json([
                'success' => true,
                'user_id' => $device->student_id,
                'user_type' => $device->user_type,
                'personal_events' => $personalEvents,
                'total_events' => count($personalEvents),
                'date_range' => [
                    'start_date' => $startDate,
                    'end_date' => $endDate
                ],
                'generated_at' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            \Log::error('Personal Calendar API error', [
                'auth_code' => $authCode,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(['error' => 'Failed to retrieve personal calendar events'], 500);
        }
    }

    /**
     * Get homework due dates for a user
     */
    private function getHomeworkDueDatesForUser($userId, $userType, $startDate = null, $endDate = null) {
        try {
            $events = [];

            if ($userType === 'student') {
                // Get homework for student
                $query = \DB::table('academic_homework as ah')
                    ->join('academic_homework_detail as ahd', 'ah.homework_id', '=', 'ahd.homework_id')
                    ->join('academic_elective_grade as eg', 'ah.grade_id', '=', 'eg.grade_id')
                    ->leftJoin('subjects as s', 'eg.subject_id', '=', 's.subject_id')
                    ->where('ahd.student_id', $userId)
                    ->where('ah.homework_status', 1) // Active homework
                    ->whereNotNull('ah.deadline');

                if ($startDate && $endDate) {
                    $query->whereBetween('ah.deadline', [$startDate, $endDate]);
                }

                $homework = $query->select(
                    'ah.homework_id',
                    'ah.title as homework_title',
                    'ah.homework_data',
                    'ah.deadline',
                    's.subject_name',
                    'eg.grade_name',
                    'ahd.is_completed',
                    'ahd.sumitted_date'
                )->get();

                foreach ($homework as $hw) {
                    $events[] = [
                        'id' => 'homework_' . $hw->homework_id,
                        'title' => $hw->homework_title . ' - Due',
                        'description' => ($hw->homework_data ?? '') . "\nSubject: " . ($hw->subject_name ?? 'Unknown'),
                        'start_date' => $hw->deadline,
                        'end_date' => $hw->deadline,
                        'start_time' => null,
                        'end_time' => null,
                        'is_all_day' => true,
                        'location' => '',
                        'status' => $hw->is_completed ? 'completed' : 'pending',
                        'created_at' => null,
                        'updated_at' => $hw->sumitted_date,
                        'source' => 'homework_due',
                        'category' => 'homework',
                        'priority' => $hw->is_completed ? 'low' : 'high',
                        'metadata' => [
                            'homework_id' => $hw->homework_id,
                            'subject_name' => $hw->subject_name,
                            'grade_name' => $hw->grade_name,
                            'is_completed' => $hw->is_completed,
                            'submitted_at' => $hw->sumitted_date
                        ]
                    ];
                }

            } elseif ($userType === 'staff') {
                // Get homework assigned by teacher
                $query = \DB::table('academic_homework as ah')
                    ->join('academic_elective_grade as eg', 'ah.grade_id', '=', 'eg.grade_id')
                    ->leftJoin('subjects as s', 'eg.subject_id', '=', 's.subject_id')
                    ->where('ah.user_id', $userId)
                    ->where('ah.homework_status', 1)
                    ->whereNotNull('ah.deadline');

                if ($startDate && $endDate) {
                    $query->whereBetween('ah.deadline', [$startDate, $endDate]);
                }

                $homework = $query->select(
                    'ah.homework_id',
                    'ah.title as homework_title',
                    'ah.homework_data',
                    'ah.deadline',
                    's.subject_name',
                    'eg.grade_name'
                )->get();

                foreach ($homework as $hw) {
                    // Count submissions
                    $totalStudents = \DB::table('academic_homework_detail')
                        ->where('homework_id', $hw->homework_id)
                        ->count();

                    $submittedCount = \DB::table('academic_homework_detail')
                        ->where('homework_id', $hw->homework_id)
                        ->where('is_completed', 1)
                        ->count();

                    $events[] = [
                        'id' => 'homework_review_' . $hw->homework_id,
                        'title' => $hw->homework_title . ' - Review Due',
                        'description' => ($hw->homework_data ?? '') . "\nSubject: " . ($hw->subject_name ?? 'Unknown') . "\nClass: " . ($hw->grade_name ?? 'N/A') . "\nSubmissions: {$submittedCount}/{$totalStudents}",
                        'start_date' => $hw->deadline,
                        'end_date' => $hw->deadline,
                        'start_time' => null,
                        'end_time' => null,
                        'is_all_day' => true,
                        'location' => '',
                        'status' => 'pending_review',
                        'created_at' => null,
                        'updated_at' => null,
                        'source' => 'homework_review',
                        'category' => 'homework',
                        'priority' => 'medium',
                        'metadata' => [
                            'homework_id' => $hw->homework_id,
                            'subject_name' => $hw->subject_name,
                            'grade_name' => $hw->grade_name,
                            'total_students' => $totalStudents,
                            'submitted_count' => $submittedCount
                        ]
                    ];
                }
            }

            return $events;

        } catch (\Exception $e) {
            \Log::error('Homework due dates retrieval error', [
                'user_id' => $userId,
                'user_type' => $userType,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Get exam/assessment schedules for a user
     */
    private function getExamScheduleForUser($userId, $userType, $startDate = null, $endDate = null) {
        try {
            $events = [];

            if ($userType === 'student') {
                // Get assessments for student
                $query = \DB::table('assessments as a')
                    ->join('academic_elective_grade as eg', 'a.grade_id', '=', 'eg.grade_id')
                    ->join('academic_elective_grade_students as egs', 'eg.grade_id', '=', 'egs.grade_id')
                    ->leftJoin('subjects as s', 'eg.subject_id', '=', 's.subject_id')
                    ->where('egs.student_id', $userId)
                    ->where('a.assessment_status', 1)
                    ->whereNotNull('a.assessment_date');

                if ($startDate && $endDate) {
                    $query->whereBetween('a.assessment_date', [$startDate, $endDate]);
                }

                $assessments = $query->select(
                    'a.assessment_id',
                    'a.assessment_title',
                    'a.assessment_description',
                    'a.assessment_date',
                    'a.assessment_time',
                    'a.assessment_type',
                    's.subject_name',
                    'eg.grade_name'
                )->distinct()->get();

                foreach ($assessments as $assessment) {
                    $events[] = [
                        'id' => 'exam_' . $assessment->assessment_id,
                        'title' => $assessment->assessment_title,
                        'description' => ($assessment->assessment_description ?? '') . "\nSubject: " . ($assessment->subject_name ?? 'Unknown') . "\nType: " . ucfirst($assessment->assessment_type ?? 'Assessment'),
                        'start_date' => $assessment->assessment_date,
                        'end_date' => $assessment->assessment_date,
                        'start_time' => $assessment->assessment_time,
                        'end_time' => $assessment->assessment_time,
                        'is_all_day' => !$assessment->assessment_time,
                        'location' => '',
                        'status' => 'scheduled',
                        'created_at' => null,
                        'updated_at' => null,
                        'source' => 'exam_schedule',
                        'category' => 'exam',
                        'priority' => 'high',
                        'metadata' => [
                            'assessment_id' => $assessment->assessment_id,
                            'subject_name' => $assessment->subject_name,
                            'grade_name' => $assessment->grade_name,
                            'assessment_type' => $assessment->assessment_type
                        ]
                    ];
                }

            } elseif ($userType === 'staff') {
                // Get assessments created by teacher
                $query = \DB::table('assessments as a')
                    ->join('academic_elective_grade as eg', 'a.grade_id', '=', 'eg.grade_id')
                    ->leftJoin('subjects as s', 'eg.subject_id', '=', 's.subject_id')
                    ->where('a.user_id', $userId)
                    ->where('a.assessment_status', 1)
                    ->whereNotNull('a.assessment_date');

                if ($startDate && $endDate) {
                    $query->whereBetween('a.assessment_date', [$startDate, $endDate]);
                }

                $assessments = $query->select(
                    'a.assessment_id',
                    'a.assessment_title',
                    'a.assessment_description',
                    'a.assessment_date',
                    'a.assessment_time',
                    'a.assessment_type',
                    's.subject_name',
                    'eg.grade_name'
                )->get();

                foreach ($assessments as $assessment) {
                    $events[] = [
                        'id' => 'exam_conduct_' . $assessment->assessment_id,
                        'title' => $assessment->assessment_title . ' - Conduct',
                        'description' => ($assessment->assessment_description ?? '') . "\nSubject: " . ($assessment->subject_name ?? 'Unknown') . "\nClass: " . ($assessment->grade_name ?? 'N/A') . "\nType: " . ucfirst($assessment->assessment_type ?? 'Assessment'),
                        'start_date' => $assessment->assessment_date,
                        'end_date' => $assessment->assessment_date,
                        'start_time' => $assessment->assessment_time,
                        'end_time' => $assessment->assessment_time,
                        'is_all_day' => !$assessment->assessment_time,
                        'location' => '',
                        'status' => 'scheduled',
                        'created_at' => null,
                        'updated_at' => null,
                        'source' => 'exam_conduct',
                        'category' => 'exam',
                        'priority' => 'high',
                        'metadata' => [
                            'assessment_id' => $assessment->assessment_id,
                            'subject_name' => $assessment->subject_name,
                            'grade_name' => $assessment->grade_name,
                            'assessment_type' => $assessment->assessment_type
                        ]
                    ];
                }
            }

            return $events;

        } catch (\Exception $e) {
            \Log::error('Exam schedule retrieval error', [
                'user_id' => $userId,
                'user_type' => $userType,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Get student birthdays for homeroom teachers
     */
    private function getStudentBirthdaysForTeacher($teacherId, $startDate = null, $endDate = null) {
        try {
            $events = [];

            // Get students in homeroom classes taught by this teacher
            $query = \DB::table('student_information as si')
                ->join('classroom as c', 'si.classroom_id', '=', 'c.classroom_id')
                ->join('users as u', 'si.id', '=', 'u.id')
                ->where('c.homeroom_teacher_id', $teacherId)
                ->whereNotNull('u.date_of_birth');

            $students = $query->select(
                'u.id as student_id',
                'u.name as student_name',
                'u.date_of_birth',
                'c.classroom_name',
                'si.branch_id'
            )->get();

            foreach ($students as $student) {
                $birthDate = $student->date_of_birth;
                $currentYear = date('Y');

                // Calculate this year's birthday
                $thisYearBirthday = $currentYear . '-' . date('m-d', strtotime($birthDate));

                // If birthday has passed this year, calculate next year's birthday
                if (strtotime($thisYearBirthday) < strtotime('today')) {
                    $nextYearBirthday = ($currentYear + 1) . '-' . date('m-d', strtotime($birthDate));
                    $birthdayToShow = $nextYearBirthday;
                } else {
                    $birthdayToShow = $thisYearBirthday;
                }

                // Filter by date range if provided
                if ($startDate && $endDate) {
                    if (strtotime($birthdayToShow) < strtotime($startDate) || strtotime($birthdayToShow) > strtotime($endDate)) {
                        continue;
                    }
                }

                // Calculate age
                $age = $currentYear - date('Y', strtotime($birthDate));
                if (date('m-d') < date('m-d', strtotime($birthDate))) {
                    $age--;
                }

                $events[] = [
                    'id' => 'birthday_' . $student->student_id,
                    'title' => $student->student_name . "'s Birthday",
                    'description' => "Student birthday\nClass: " . ($student->classroom_name ?? 'N/A') . "\nTurning: " . ($age + 1) . " years old",
                    'start_date' => $birthdayToShow,
                    'end_date' => $birthdayToShow,
                    'start_time' => null,
                    'end_time' => null,
                    'is_all_day' => true,
                    'location' => '',
                    'status' => 'scheduled',
                    'created_at' => null,
                    'updated_at' => null,
                    'source' => 'student_birthday',
                    'category' => 'birthday',
                    'priority' => 'low',
                    'metadata' => [
                        'student_id' => $student->student_id,
                        'student_name' => $student->student_name,
                        'classroom_name' => $student->classroom_name,
                        'age_turning' => $age + 1,
                        'original_birth_date' => $birthDate
                    ]
                ];
            }

            return $events;

        } catch (\Exception $e) {
            \Log::error('Student birthdays retrieval error', [
                'teacher_id' => $teacherId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
 }

