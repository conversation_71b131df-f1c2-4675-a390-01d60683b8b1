<?php

namespace App\Http\Controllers;

use App\Library\Helper\AcademicHelper;
use App\Models\AcademicWeek;
use App\Models\DisciplineRecord;
use App\Models\StudentClassAttendance;
use App\Models\Timetable;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\MobileNotification;
use App\Models\MobileDevice;
use App\Library\Repository\MobileNotificationRepository;
use App\Library\Repository\MobileApiRepository;
use App\Library\Repository\GoogleDriveRepository;
use App\Library\Services\GoogleDriveService;
use App\Http\Requests\MobileStudentHealthRecordRequest;
use App\Http\Requests\MobileStaffHealthRecordRequest;
use App\Http\Requests\MobileGuestHealthRecordRequest;
use Illuminate\Support\Facades\Log;
use App\Http\Requests\MobileStudentHealthInfoRequest;

class MobileApiController extends Controller
{
    protected MobileNotificationRepository $mobileNotification;
    protected GoogleDriveRepository $driveRepository;

    public function __construct(MobileNotificationRepository $mobileNotification, GoogleDriveRepository $driveRepository = null)
    {
        $this->mobileNotification = $mobileNotification;

        // Initialize GoogleDriveRepository if not provided
        if ($driveRepository === null) {
            try {
                $driveService = new GoogleDriveService();
                $academicHelper = new \App\Library\Helper\AcademicHelper();
                $this->driveRepository = new GoogleDriveRepository($driveService, $academicHelper);
            } catch (\Exception $e) {
                \Log::warning('Failed to initialize Google Drive service in MobileApiController: ' . $e->getMessage());
                $this->driveRepository = null;
            }
        } else {
            $this->driveRepository = $driveRepository;
        }
    }

    /**
     * Check if user has homework Google Drive permissions
     */
    protected function checkHomeworkGoogleDriveAccess($userId, $branchId, $accessLevel = 'read')
    {
        // Get user information
        $user = \DB::table('users')->where('id', $userId)->first();

        // Admin users have full access
        if ($user && $user->admin == 1) {
            return true;
        }

        // Students have different access logic - they can access homework if they're active students in the branch
        if ($user && $user->user_type === 'student') {
            // Check if student is active and belongs to the branch
            $isActiveStudent = \DB::table('student_information')
                ->where('id', $userId)
                ->where('branch_id', $branchId)
                ->where('student_status', 1) // Active student
                ->exists();

            if ($isActiveStudent) {
                Log::debug('Homework Google Drive access granted to active student', [
                    'user_id' => $userId,
                    'branch_id' => $branchId,
                    'access_level' => $accessLevel,
                    'user_type' => $user->user_type
                ]);
                return true; // Students can read and write (submit) homework
            } else {
                Log::debug('Homework Google Drive access denied - student not active or not in branch', [
                    'user_id' => $userId,
                    'branch_id' => $branchId,
                    'access_level' => $accessLevel,
                    'user_type' => $user->user_type
                ]);
                return false;
            }
        }

        // Check if user has Google Drive module permission (for staff/teachers)
        $hasAccess = \DB::table('permissions')
                       ->leftJoin('roles_user', 'roles_user.role_id', 'permissions.role_id')
                       ->where('permissions.module_id', 999) // Google Drive module ID
                       ->where('roles_user.user_id', $userId)
                       ->where('roles_user.branch_id', $branchId)
                       ->where('permissions.p1', 1) // Basic view permission
                       ->count() > 0;

        // For staff/teachers, if they don't have specific Google Drive permissions,
        // check if they have a role in the branch (more flexible approach)
        if (!$hasAccess && $user && ($user->user_type === 'staff' || $user->user_type === 'teacher')) {
            $hasRoleInBranch = \DB::table('roles_user')
                ->where('user_id', $userId)
                ->where('branch_id', $branchId)
                ->exists();

            if ($hasRoleInBranch) {
                Log::debug('Homework Google Drive access granted via role in branch', [
                    'user_id' => $userId,
                    'branch_id' => $branchId,
                    'access_level' => $accessLevel,
                    'user_type' => $user->user_type
                ]);
                return true; // Grant access to staff/teachers with roles in the branch
            }
        }

        if (!$hasAccess) {
            Log::debug('Homework Google Drive access denied - no permissions', [
                'user_id' => $userId,
                'branch_id' => $branchId,
                'access_level' => $accessLevel,
                'user_type' => $user ? $user->user_type : 'unknown'
            ]);
            return false;
        }

        // For write operations, check additional permissions
        if (in_array($accessLevel, ['write', 'full'])) {
            // Students already have write access if they passed the basic access check above
            if ($user && $user->user_type === 'student') {
                return true; // Students can submit homework if they have basic access
            }

            $hasWriteAccess = \DB::table('permissions')
                                ->leftJoin('roles_user', 'roles_user.role_id', 'permissions.role_id')
                                ->where('permissions.module_id', 999)
                                ->where('roles_user.user_id', $userId)
                                ->where('roles_user.branch_id', $branchId)
                                ->where('permissions.p2', 1) // Create/edit permission
                                ->count() > 0;

            // For staff/teachers, if they don't have specific write permissions,
            // but have basic access, allow write operations for homework
            if (!$hasWriteAccess && $user && ($user->user_type === 'staff' || $user->user_type === 'teacher')) {
                Log::debug('Homework Google Drive write access granted to staff/teacher', [
                    'user_id' => $userId,
                    'branch_id' => $branchId,
                    'user_type' => $user->user_type
                ]);
                return true; // Allow staff/teachers to write to homework folders
            }

            return $hasWriteAccess;
        }

        return true;
    }

    /**
     * Get homework folder upload restrictions based on user role
     */
    protected function getHomeworkUploadRestrictions($userId, $branchId)
    {
        try {
            // Check if user is admin
            $user = \DB::table('users')->where('id', $userId)->first();
            if ($user && $user->admin == 1) {
                return [
                    'max_file_size_mb' => 100,
                    'allowed_file_types' => ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'jpg', 'jpeg', 'png', 'gif', 'mp4', 'avi', 'zip', 'rar']
                ];
            }

        // Get role-based restrictions for homework folder type
        $restrictions = \DB::table('google_drive_role_permissions')
                          ->join('roles_user', 'roles_user.role_id', '=', 'google_drive_role_permissions.role_id')
                          ->where('roles_user.user_id', $userId)
                          ->where('roles_user.branch_id', $branchId)
                          ->where('google_drive_role_permissions.folder_type', 'homework')
                          ->where('google_drive_role_permissions.is_active', 1)
                          ->first();

        // Handle case where no restrictions are found
        if (!$restrictions) {
            return [
                'max_file_size_mb' => 10,
                'allowed_file_types' => ['pdf', 'doc', 'docx', 'txt', 'jpg', 'jpeg', 'png']
            ];
        }

        // Additional safety check - ensure $restrictions is still an object
        if (!is_object($restrictions)) {
            \Log::warning('Restrictions is not an object after null check', [
                'user_id' => $userId,
                'branch_id' => $branchId,
                'restrictions_type' => gettype($restrictions),
                'restrictions_value' => $restrictions
            ]);

            return [
                'max_file_size_mb' => 10,
                'allowed_file_types' => ['pdf', 'doc', 'docx', 'txt', 'jpg', 'jpeg', 'png']
            ];
        }

        // Parse allowed file types safely
        $allowedTypes = ['pdf', 'doc', 'docx', 'txt', 'jpg', 'jpeg', 'png']; // Default
        if (isset($restrictions->allowed_file_types) && !empty($restrictions->allowed_file_types)) {
            $decodedTypes = json_decode($restrictions->allowed_file_types, true);
            if (is_array($decodedTypes) && !empty($decodedTypes)) {
                $allowedTypes = $decodedTypes;
            }
        }

        return [
            'max_file_size_mb' => isset($restrictions->max_file_size_mb) && is_numeric($restrictions->max_file_size_mb)
                ? (int)$restrictions->max_file_size_mb
                : 10,
            'allowed_file_types' => $allowedTypes
        ];

        } catch (\Exception $e) {
            \Log::error('Error in getHomeworkUploadRestrictions', [
                'user_id' => $userId,
                'branch_id' => $branchId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Return safe defaults on any error
            return [
                'max_file_size_mb' => 10,
                'allowed_file_types' => ['pdf', 'doc', 'docx', 'txt', 'jpg', 'jpeg', 'png']
            ];
        }
    }

    /**
     * Validate homework file upload against restrictions
     */
    protected function validateHomeworkFileUpload($file, $restrictions)
    {
        // Ensure restrictions is an array with required keys
        if (!is_array($restrictions) || !isset($restrictions['max_file_size_mb']) || !isset($restrictions['allowed_file_types'])) {
            return [
                'valid' => false,
                'error' => 'Invalid file upload restrictions configuration'
            ];
        }

        // Check file size
        $fileSizeMB = $file->getSize() / 1024 / 1024;
        if ($fileSizeMB > $restrictions['max_file_size_mb']) {
            return [
                'valid' => false,
                'error' => "File size (" . number_format($fileSizeMB, 2) . "MB) exceeds maximum allowed size ({$restrictions['max_file_size_mb']}MB)"
            ];
        }

        // Check file type
        $extension = strtolower($file->getClientOriginalExtension());
        if (!in_array($extension, $restrictions['allowed_file_types'])) {
            return [
                'valid' => false,
                'error' => "File type '{$extension}' is not allowed. Allowed types: " . implode(', ', $restrictions['allowed_file_types'])
            ];
        }

        return ['valid' => true];
    }

    public function checkStudentCredentials()
    {
        $username = request('username');
        $password = request('password');
        $deviceTokenRaw = request('deviceToken');
        $deviceType = request('deviceType', 'ios'); // Use 'deviceType' parameter, default to 'ios'
        $deviceName = request('deviceName', ''); // Get device name

        // Validate device token
        if (empty($deviceTokenRaw)) {
            return response()->json(['error' => 'Device token is required'], 400);
        }

        $deviceToken = base64_decode($deviceTokenRaw);

        // Validate decoded device token
        if (empty($deviceToken) || strlen($deviceToken) < 10) {
            return response()->json(['error' => 'Invalid device token'], 400);
        }

        // Validate device type
        if (!in_array(strtolower($deviceType), ['ios', 'android'])) {
            return response()->json(['error' => 'Invalid device type. Must be ios or android'], 400);
        }

        $rd = null;
        $arr = null;
        if (\Auth::attempt(array('username' => $username, 'password' => $password))) {
            $userInfo = User::where('username', $username)->first();

            if (!$userInfo) {
                return response()->json(['error' => 'User information not found'], 404);
            }

            // Check if device already exists for this user and device token
            $existingDevice = MobileDevice::where('student_id', $userInfo->id)
                                        ->where('device_token', $deviceToken)
                                        ->where('user_type', 'student')
                                        ->first();

            if ($existingDevice) {
                // Update existing device with new auth code, device info and timestamp
                $existingDevice->update([
                    'auth_code' => uniqid(),
                    'device_type' => strtolower($deviceType),
                    'device_name' => $deviceName,
                    'last_login' => now(),
                    'updated_at' => now()
                ]);
                $device = $existingDevice;
            } else {
                // Create new device record
                $device = MobileDevice::create([
                    'auth_code' =>  uniqid(),
                    'student_id'    => $userInfo->id,
                    'device_type'   => strtolower($deviceType),
                    'device_name'   => $deviceName,
                    'user_type'   => 'student',
                    'device_token' => $deviceToken,
                    'last_login' => now()
                ]);
            }

            if (!$device) {
                return response()->json(['error' => 'Failed to create or update device record'], 500);
            }

            // Check if user is a homeroom teacher
            $isHomeroom = false;
            if ($userInfo->user_type === 'staff') {
                $academicHelper = new \App\Library\Helper\AcademicHelper();
                $homeroomClassrooms = $academicHelper->homeroomClassrooms($userInfo->id);
                $isHomeroom = $homeroomClassrooms->count() > 0;
            }

            // Get branch information
            $branchInfo = null;
            if ($userInfo->branch_id) {
                $branch = \App\Models\Branch::find($userInfo->branch_id);
                if ($branch) {
                    $branchInfo = [
                        'branch_id' => $branch->branch_id,
                        'branch_name' => $branch->branch_name,
                        'branch_description' => $branch->branch_description ?? null,
                        'branch_code' => $branch->branch_code ?? null,
                        'branch_address' => $branch->branch_address ?? null
                    ];
                }
            }

            $arr = array(
                "name"      => $userInfo->name,
                "photo"     => $userInfo->photo ? "https://sis.bfi.edu.mm" . $userInfo->photo : null,
                "authCode"  => $device->auth_code,
                "id"        => $username,
                "user_id"   => $userInfo->id,
                "username"  => $userInfo->username,
                "email"     => $userInfo->email,
                "mobile_phone" => $userInfo->mobile_phone ?? $userInfo->phone,
                "user_type" => $userInfo->user_type,
                "user_status" => $userInfo->user_status,
                "gender"    => $userInfo->gender,
                "birth_date" => $userInfo->birth_date,
                "nationality" => $userInfo->nationality,
                "address"   => $userInfo->address,
                "emergency_contact" => $userInfo->emergency_contact,
                "rfid"      => $userInfo->rfid,
                "branch"    => $branchInfo,
                "is_homeroom" => $isHomeroom,
                "last_login" => $device->last_login,
                "device_info" => [
                    'device_type' => $device->device_type,
                    'device_name' => $device->device_name
                ]
            );
            $returnData = $arr;
        } else {
            \Log::warning('Mobile authentication failed', [
                'username' => $username,
                'device_token' => substr($deviceToken, 0, 20) . '...',
                'device_type' => $deviceType,
                'ip' => request()->ip()
            ]);
            return response()->json(['error' => 'Invalid username or password'], 401);
        }
        return $returnData;
    }

    public function getNotifications()
    {
        $userArr = explode('|', request('username'));
        $notifications = MobileNotification::whereIn('user_id', $userArr)
            ->orWhere('notification_type', 'all')
            ->groupBy('notification_uid')
            ->orderBy('created_at', 'desc')
            ->take(30)
            ->get();

        $myObj = new \stdClass;
        $jsonString = null;
        $counter = 0;

        foreach ($notifications as $key => $notification) {
            $notificationTimeArr = explode(" ", $notification->created_at);
            $myObj->notificationTitle = $notification->notification_title;
            $myObj->notificationBody = $notification->notification_body;
            $myObj->notificationDate = $notificationTimeArr[0];
            $myObj->notificationTime = $notificationTimeArr[1];
            $myObj->notificationType = $notification->notification_type;
            $myJson = json_encode($myObj);
            $jsonString = $jsonString . $myJson;
            $counter++;
        }

        if ($counter == 0) {
            $myObj->notificationTitle = "No new notifications";
            $myObj->notificationBody = "We could not find any new notifications.";
            $myObj->notificationDate = " ";
            $myObj->notificationTime = " ";
            $myObj->notificationType = "all";
            $myJson = json_encode($myObj);
            $jsonString = $jsonString . $myJson;
        }
        $jsonString = str_replace("}{", "},{", $jsonString);
        $jsonString = "[" . $jsonString . "]";
        return $jsonString;
    }

    public function checkStaffCredentials()
    {
        $username = request('username');
        $password = request('password');
        $deviceTokenRaw = request('deviceToken');
        $deviceType = request('deviceType', 'ios'); // Use 'deviceType' parameter, default to 'ios'
        $deviceName = request('deviceName', ''); // Get device name

        // Validate device token
        if (empty($deviceTokenRaw)) {
            return response()->json(['error' => 'Device token is required'], 400);
        }

        $deviceToken = base64_decode($deviceTokenRaw);

        // Validate decoded device token
        if (empty($deviceToken) || strlen($deviceToken) < 10) {
            return response()->json(['error' => 'Invalid device token'], 400);
        }

        // Validate device type
        if (!in_array(strtolower($deviceType), ['ios', 'android'])) {
            return response()->json(['error' => 'Invalid device type. Must be ios or android'], 400);
        }

        $rd = null;
        $arr = null;
        if (\Auth::attempt(array('username' => $username, 'password' => $password))) {
            $userInfo = User::where('username', $username)->first();

            if (!$userInfo) {
                return response()->json(['error' => 'User information not found'], 404);
            }

            // Get user roles with branch and role details
            $userRoles = \App\Models\UserRole::leftJoin('roles', 'roles.role_id', 'roles_user.role_id')
                ->leftJoin('branches', 'branches.branch_id', 'roles_user.branch_id')
                ->where('roles_user.user_id', $userInfo->id)
                ->select(
                    'roles_user.role_user_id',
                    'roles_user.role_id',
                    'roles_user.branch_id',
                    'roles.role_name',
                    'branches.branch_name'
                )
                ->get();

            // Format roles data
            $rolesData = [];
            foreach ($userRoles as $role) {
                $rolesData[] = [
                    'role_user_id' => $role->role_user_id,
                    'role_id' => $role->role_id,
                    'role_name' => $role->role_name,
                    'branch_id' => $role->branch_id,
                    'branch_name' => $role->branch_name
                ];
            }

            // Check if device already exists for this user and device token
            $existingDevice = MobileDevice::where('student_id', $userInfo->id)
                                        ->where('device_token', $deviceToken)
                                        ->where('user_type', 'staff')
                                        ->first();

            if ($existingDevice) {
                // Update existing device with new auth code, device info and timestamp
                $existingDevice->update([
                    'auth_code' => uniqid(),
                    'device_type' => strtolower($deviceType),
                    'device_name' => $deviceName,
                    'last_login' => now(),
                    'updated_at' => now()
                ]);
                $device = $existingDevice;
            } else {
                // Create new device record
                $device = MobileDevice::create([
                    'auth_code' =>  uniqid(),
                    'student_id'    => $userInfo->id,
                    'device_type'   => strtolower($deviceType),
                    'device_name'   => $deviceName,
                    'user_type'   => 'staff',
                    'device_token' => $deviceToken,
                    'last_login' => now()
                ]);
            }

            if (!$device) {
                return response()->json(['error' => 'Failed to create or update device record'], 500);
            }

            // Check if user is a homeroom teacher
            $isHomeroom = false;
            if ($userInfo->user_type === 'staff') {
                $academicHelper = new \App\Library\Helper\AcademicHelper();
                $homeroomClassrooms = $academicHelper->homeroomClassrooms($userInfo->id);
                $isHomeroom = $homeroomClassrooms->count() > 0;
            }

            // Get branch information
            $branchInfo = null;
            if ($userInfo->branch_id) {
                $branch = \App\Models\Branch::find($userInfo->branch_id);
                if ($branch) {
                    $branchInfo = [
                        'branch_id' => $branch->branch_id,
                        'branch_name' => $branch->branch_name,
                        'branch_description' => $branch->branch_description ?? null,
                        'branch_code' => $branch->branch_code ?? null,
                        'branch_address' => $branch->branch_address ?? null
                    ];
                }
            }

            $arr = array(
                "name"      => $userInfo->name,
                "photo"     => $userInfo->photo ? "https://sis.bfi.edu.mm" . $userInfo->photo : null,
                "authCode"  => $device->auth_code,
                "id"        => $username,
                "user_id"   => $userInfo->id,
                "username"  => $userInfo->username,
                "email"     => $userInfo->email,
                "mobile_phone" => $userInfo->mobile_phone ?? $userInfo->phone,
                "user_type" => $userInfo->user_type,
                "user_status" => $userInfo->user_status,
                "gender"    => $userInfo->gender,
                "birth_date" => $userInfo->birth_date,
                "nationality" => $userInfo->nationality,
                "address"   => $userInfo->address,
                "emergency_contact" => $userInfo->emergency_contact,
                "rfid"      => $userInfo->rfid,
                "biometric_id" => $userInfo->biometric_id,
                "admin"     => $userInfo->admin == 1 ? true : false,
                "branch"    => $branchInfo,
                "roles"     => $rolesData,
                "total_roles" => count($rolesData),
                "is_homeroom" => $isHomeroom,
                "last_login" => $device->last_login,
                "device_info" => [
                    'device_type' => $device->device_type,
                    'device_name' => $device->device_name
                ]
            );
            $returnData = $arr;
        } else {
            \Log::warning('Mobile staff authentication failed', [
                'username' => $username,
                'device_token' => substr($deviceToken, 0, 20) . '...',
                'device_type' => $deviceType,
                'ip' => request()->ip()
            ]);
            return response()->json(['error' => 'Invalid username or password'], 401);
        }
        return $returnData;
    }

    public function storeApiClassAttendance()
    {
        $timetableId = request('timetable');
        $attendance = request('attendance');
        $topic      = request('topic');

        $timetable = Timetable::find($timetableId);
        $attendanceInfo = explode("/", $attendance);
        $branchId = $timetable->branch_id;
        $academicYearId = $timetable->academic_year_id;
        $today = date('Y-m-d');
        $weekInfo = AcademicWeek::where('start_date', '<=', $today)
            ->where('end_date', '>=', $today)
            ->where('branch_id', $branchId)
            ->first();
        $currentWeek = $weekInfo->week;

        // Track notification results
        $notificationResults = [];
        $totalStudents = 0;
        $notificationsSent = 0;
        $notificationsFailed = 0;

        foreach ($attendanceInfo as $info) {
            if ($info != '') {
                $arr = explode("|", $info);
                $totalStudents++;

                $attendanceRecord = StudentClassAttendance::updateOrCreate(
                    [
                        'academic_year_id'  => $academicYearId,
                        'week_day'          => $timetable->week_day,
                        'week_time'         => $timetable->week_time,
                        'subject_id'        => $timetable->subject_id,
                        'teacher_id'        => $timetable->user_id,
                        'grade_id'          => $timetable->grade_id,
                        'week'              => $currentWeek,
                        'student_id'        => $arr[0]
                    ],
                    [
                        'date'              => date('Y-m-d'),
                        'attendance_status' => $arr[1],
                        'attendance_note'   => $arr[2]
                    ]
                );

                // Enhanced attendance notifications for absent and late students
                $notificationResult = $this->sendAttendanceNotification($arr, $timetable, $attendanceRecord);

                if ($notificationResult['notification_sent']) {
                    $notificationsSent++;
                } elseif ($notificationResult['notification_attempted']) {
                    $notificationsFailed++;
                }

                $notificationResults[] = [
                    'student_id' => $arr[0],
                    'attendance_status' => $arr[1],
                    'notification_sent' => $notificationResult['notification_sent'],
                    'notification_attempted' => $notificationResult['notification_attempted'],
                    'notification_message' => $notificationResult['message']
                ];
            }
        }

        return response()->json([
            'success' => true,
            'message' => 'Attendance stored successfully',
            'data' => [
                'total_students' => $totalStudents,
                'notifications_sent' => $notificationsSent,
                'notifications_failed' => $notificationsFailed,
                'notification_details' => $notificationResults
            ]
        ]);
    }

    /**
     * Send attendance notifications for absent and late students
     */
    private function sendAttendanceNotification($attendanceData, $timetable, $attendanceRecord)
    {
        $studentId = $attendanceData[0];
        $attendanceStatus = $attendanceData[1];
        $attendanceNote = $attendanceData[2] ?? '';

        // Only send notifications for absent and late students
        if (!in_array($attendanceStatus, ['absent', 'late'])) {
            return [
                'notification_sent' => false,
                'notification_attempted' => false,
                'message' => 'No notification needed for status: ' . $attendanceStatus
            ];
        }

        // Check if student is already marked absent for the entire day
        // If so, don't send individual class absence notifications
        if ($attendanceStatus === 'absent') {
            $currentDate = date('Y-m-d');
            $ah = new AcademicHelper();
            $academicYearId = $ah->branchAcademicYear($timetable->branch_id);

            $dailyAttendance = \App\Models\StudentAttendance::where('student_id', $studentId)
                ->where('date', $currentDate)
                ->where('academic_year_id', $academicYearId)
                ->where('attendance_status', 'absent')
                ->first();

            if ($dailyAttendance) {
                return [
                    'notification_sent' => false,
                    'notification_attempted' => false,
                    'message' => 'Student already marked absent for the day - no class notification sent'
                ];
            }
        }

        try {
            // Get student information
            $student = User::where('id', $studentId)
                          ->where('user_status', 1)
                          ->first();

            if (!$student) {
                return [
                    'notification_sent' => false,
                    'notification_attempted' => true,
                    'message' => 'Student not found: ' . $studentId
                ];
            }

            // Get subject and grade information
            $subject = \App\Models\Subject::find($timetable->subject_id);
            $grade = \App\Models\ElectiveGrade::where('grade_id', $timetable->grade_id)->first();
            $teacher = User::find($timetable->user_id);

            // Prepare notification data
            $subjectName = $subject ? $subject->subject_name : 'Unknown Subject';
            $gradeName = $grade ? $grade->grade_name : 'Unknown Grade';
            $teacherName = $teacher ? $teacher->name : 'Unknown Teacher';
            $currentDate = date('Y-m-d');
            $currentTime = date('H:i');

            // Create different messages based on attendance status
            $title = $attendanceStatus === 'absent' ? 'Absence Alert' : 'Late Arrival Alert';

            if ($attendanceStatus === 'absent') {
                $message = "{$student->name} was marked as ABSENT in {$subjectName} ({$gradeName}) class on {$currentDate} at {$currentTime}.";
                $priority = 'high';
                $category = 'attendance';
            } else { // late
                $message = "{$student->name} was marked as LATE in {$subjectName} ({$gradeName}) class on {$currentDate} at {$currentTime}.";
                $priority = 'normal';
                $category = 'attendance';
            }

            // Add note if provided
            if (!empty($attendanceNote)) {
                $message .= " Note: {$attendanceNote}";
            }

            // Send notification to student
            $notificationData = [
                'student' => $studentId,
                'type' => 'attendance_' . $attendanceStatus,
                'title' => $title,
                'message' => $message,
                'user_type' => 'student',
                'priority' => $priority,
                'category' => $category,
                'data' => [
                    'attendance_id' => $attendanceRecord->attendance_id ?? null,
                    'attendance_status' => $attendanceStatus,
                    'subject_name' => $subjectName,
                    'grade_name' => $gradeName,
                    'teacher_name' => $teacherName,
                    'date' => $currentDate,
                    'time' => $currentTime,
                    'period' => $timetable->week_time,
                    'note' => $attendanceNote
                ]
            ];

            // Send real-time notification
            $this->mobileNotification->sendRealTime($notificationData);

            return [
                'notification_sent' => true,
                'notification_attempted' => true,
                'message' => "Notification sent successfully for {$attendanceStatus} status"
            ];

        } catch (\Exception $e) {
            return [
                'notification_sent' => false,
                'notification_attempted' => true,
                'message' => 'Failed to send notification: ' . $e->getMessage()
            ];
        }
    }



    /**
     * Store BPS records with comprehensive validation and business logic
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function storeBps(Request $request)
    {
        // Optional CSRF token validation for enhanced security
        $csrfToken = $request->input('csrf_token');
        if ($csrfToken) {
            // Validate CSRF token if provided
            if (!hash_equals(csrf_token(), $csrfToken)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid CSRF token',
                    'error_code' => 'CSRF_TOKEN_MISMATCH'
                ], 422);
            }
        }

        // Validate auth_code for mobile authentication
        $authCode = $request->input('auth_code');
        if (!$authCode) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code is required',
                'error_code' => 'AUTH_CODE_REQUIRED'
            ], 401);
        }

        // Verify the auth_code belongs to a valid staff member
        $device = MobileDevice::where('auth_code', $authCode)->first();
        if (!$device || $device->user_type !== 'staff') {
            return response()->json([
                'success' => false,
                'message' => 'Invalid authentication code or insufficient permissions',
                'error_code' => 'INVALID_AUTH_CODE'
            ], 401);
        }

        // Validate required parameters
        $requiredFields = ['branch_id', 'case_type', 'date'];
        foreach ($requiredFields as $field) {
            if (!$request->has($field) || $request->input($field) === null) {
                return response()->json([
                    'success' => false,
                    'message' => "Missing required field: {$field}",
                    'error_code' => 'MISSING_REQUIRED_FIELD'
                ], 400);
            }
        }

        // Validate case_type
        $caseType = $request->input('case_type');
        if (!in_array($caseType, ['0', '1'])) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid case_type. Must be "0" for PRS or "1" for DPS',
                'error_code' => 'INVALID_CASE_TYPE'
            ], 400);
        }

        // Validate date format and ensure it's not in the future
        $date = $request->input('date');
        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid date format. Use YYYY-MM-DD',
                'error_code' => 'INVALID_DATE_FORMAT'
            ], 400);
        }

        if (strtotime($date) > strtotime(date('Y-m-d'))) {
            return response()->json([
                'success' => false,
                'message' => 'Date cannot be in the future',
                'error_code' => 'FUTURE_DATE_NOT_ALLOWED'
            ], 400);
        }

        $repository = new MobileApiRepository();

        // Unified data structure that handles both single and multiple BPS records
        $data = [
            'branch_id' => $request->input('branch_id'),
            'case_type' => $caseType,
            'date' => $date,
            'note' => $request->input('note', ''),
            'user_id' => $device->student_id, // Use authenticated user ID from device
            'academic_semester' => $request->input('academic_semester'), // Optional, will use current if not provided

            // Multiple format (arrays)
            'students' => $request->input('students', []),
            'items' => $request->input('items', []),

            // Single format (individual IDs)
            'student_id' => $request->input('student_id'),
            'item_id' => $request->input('item_id'),

            // Legacy format support
            'student' => $request->input('student', []),
            'dps_case' => $request->input('dps_case'),
            'prs_case' => $request->input('prs_case'),

            // Security context
            'auth_code' => $authCode,
            'csrf_token_provided' => !empty($csrfToken)
        ];

        $result = $repository->storeBps($data);

        // Add notification status tracking to the response
        if ($result['success'] && isset($result['results'])) {
            $notificationsSent = 0;
            $notificationsFailed = 0;
            $notificationDetails = [];

            foreach ($result['results'] as $record) {
                if (isset($record['notification_sent'])) {
                    if ($record['notification_sent']) {
                        $notificationsSent++;
                    } else {
                        $notificationsFailed++;
                    }

                    $notificationDetails[] = [
                        'student_id' => $record['student_id'],
                        'bps_record_id' => $record['record_id'],
                        'notification_sent' => $record['notification_sent'],
                        'notification_message' => $record['notification_message'] ?? 'BPS notification processed'
                    ];
                }
            }

            $result['notification_summary'] = [
                'total_notifications_sent' => $notificationsSent,
                'total_notifications_failed' => $notificationsFailed,
                'notification_details' => $notificationDetails
            ];
        }

        // Add security information to response
        $result['security_info'] = array_merge($result['security_info'] ?? [], [
            'csrf_token_validated' => !empty($csrfToken),
            'auth_code_validated' => true,
            'authenticated_user_id' => $device->student_id,
            'request_timestamp' => now()->toISOString()
        ]);

        // Return appropriate HTTP status code based on result
        $statusCode = $result['success'] ? 200 : 400;
        if (isset($result['error_code'])) {
            switch ($result['error_code']) {
                case 'INVALID_AUTH_CODE':
                case 'PERMISSION_DENIED':
                    $statusCode = 401;
                    break;
                case 'INVALID_ITEM':
                case 'NO_STUDENTS':
                case 'NO_ITEM':
                    $statusCode = 404;
                    break;
                case 'SERVER_ERROR':
                    $statusCode = 500;
                    break;
            }
        }

        return response()->json($result, $statusCode);
    }

    /**
     * Delete BPS record with proper authentication and permission checking
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteBps()
    {
        try {
            // Handle both parameter naming conventions
            $bpsId = request('bpsId') ?? request('dicipline_record_id') ?? request('discipline_record_id');
            $authCode = request('authCode') ?? request('authcode') ?? request('auth_code');

            // Validate required parameters
            if (!$bpsId) {
                return response()->json([
                    'success' => false,
                    'message' => 'BPS ID is required (bpsId, dicipline_record_id, or discipline_record_id)',
                    'error_code' => 'BPS_ID_REQUIRED'
                ], 400);
            }

            if (!$authCode) {
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication code is required (authCode, authcode, or auth_code)',
                    'error_code' => 'AUTH_CODE_REQUIRED'
                ], 401);
            }

            // Verify the auth_code belongs to a valid staff member
            $device = MobileDevice::where('auth_code', $authCode)->first();
            if (!$device || $device->user_type !== 'staff') {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid authentication code or insufficient permissions',
                    'error_code' => 'INVALID_AUTH_CODE'
                ], 401);
            }

            // Get the staff user information
            $staffUser = User::find($device->student_id);
            if (!$staffUser) {
                return response()->json([
                    'success' => false,
                    'message' => 'Staff user not found',
                    'error_code' => 'USER_NOT_FOUND'
                ], 404);
            }

            // Check if BPS record exists
            $bpsRecord = DisciplineRecord::find($bpsId);
            if (!$bpsRecord) {
                return response()->json([
                    'success' => false,
                    'message' => 'BPS record not found',
                    'error_code' => 'BPS_NOT_FOUND'
                ], 404);
            }

            // Use the BPS repository for proper deletion logic (handles award points)
            $academicHelper = new \App\Library\Helper\AcademicHelper();
            $mobileNotificationRepository = new \App\Library\Repository\MobileNotificationRepository($academicHelper);
            $bpsRepository = new \App\Library\Repository\BpsRepository($academicHelper, $mobileNotificationRepository);
            $bpsRepository->deleteBps($bpsId);

            return response()->json([
                'success' => true,
                'message' => 'BPS record deleted successfully',
                'data' => [
                    'deleted_bps_id' => $bpsId,
                    'deleted_by' => $staffUser->name,
                    'deleted_at' => now()->toISOString()
                ]
            ]);

        } catch (\Exception $e) {


            return response()->json([
                'success' => false,
                'message' => 'Failed to delete BPS record',
                'error_code' => 'DELETION_FAILED',
                'error_details' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check if the authenticated user has permission to delete BPS records
     * This endpoint helps the frontend determine whether to show delete buttons
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkBpsDeletePermission()
    {
        try {
            $authCode = request('authCode');

            if (!$authCode) {
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication code is required',
                    'error_code' => 'AUTH_CODE_REQUIRED'
                ], 401);
            }

            // Verify the auth_code belongs to a valid staff member
            $device = MobileDevice::where('auth_code', $authCode)->first();
            if (!$device || $device->user_type !== 'staff') {
                return response()->json([
                    'success' => false,
                    'has_permission' => false,
                    'message' => 'Invalid authentication code or not a staff member'
                ]);
            }

            // Get the staff user
            $staffUser = User::find($device->student_id);
            if (!$staffUser) {
                return response()->json([
                    'success' => false,
                    'has_permission' => false,
                    'message' => 'Staff user not found'
                ]);
            }

            // Check BPS delete permission (module 56, action 2)
            $module = 56;
            $action = 2;
            $colName = "p" . $action;
            $userId = $staffUser->id;

            // Get branch ID using the same logic as getTeacherBpsData
            $branchId = null;
            if ($staffUser->branch_id) {
                $branchId = $staffUser->branch_id;
            } else {
                // Fallback: get first branch from user's branches
                $userBranch = \DB::table('users_branches')
                    ->where('user_id', $userId)
                    ->first();
                if ($userBranch) {
                    $branchId = $userBranch->branch_id;
                }
            }

            if (!$branchId) {
                return response()->json([
                    'success' => false,
                    'has_permission' => false,
                    'message' => 'No branch information available for permission check'
                ]);
            }

            // Check permission
            $hasPermission = \App\Models\Permission::leftJoin('roles_user', 'roles_user.role_id', 'permissions.role_id')
                                                  ->where('permissions.module_id', $module)
                                                  ->where('roles_user.user_id', $userId)
                                                  ->where('roles_user.branch_id', $branchId)
                                                  ->where('permissions.' . $colName, 1)
                                                  ->count() > 0;

            return response()->json([
                'success' => true,
                'has_permission' => $hasPermission,
                'permission_details' => [
                    'module_id' => $module,
                    'action_id' => $action,
                    'permission_name' => 'BPS Delete Permission',
                    'user_id' => $userId,
                    'user_name' => $staffUser->name,
                    'branch_id' => $branchId
                ]
            ]);

        } catch (\Exception $e) {


            return response()->json([
                'success' => false,
                'has_permission' => false,
                'message' => 'Permission check failed',
                'error_code' => 'PERMISSION_CHECK_FAILED'
            ], 500);
        }
    }





    /**
     * Mark homework as viewed when student opens it
     */
    public function markHomeworkAsViewed(Request $request)
    {
        $authCode = $request->input('auth_code');
        $detailId = $request->input('detail_id');

        if (!$authCode || !$detailId) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code and detail ID are required'
            ], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->markHomeworkAsViewed($authCode, $detailId);
    }

    /**
     * Submit homework by student
     */
    public function submitHomework(Request $request)
    {
        $authCode = $request->input('auth_code');

        if (!$authCode) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code is required'
            ], 400);
        }

        // Validate required fields
        $request->validate([
            'detail_id' => 'required|integer',
            'reply_file' => 'nullable|string',
            'reply_data' => 'nullable|string'
        ]);

        // At least one submission field is required
        if (empty($request->input('reply_file')) && empty($request->input('reply_data'))) {
            return response()->json([
                'success' => false,
                'message' => 'At least one submission (file or data) is required'
            ], 400);
        }

        $data = [
            'detail_id' => $request->input('detail_id'),
            'reply_file' => $request->input('reply_file'),
            'reply_data' => $request->input('reply_data')
        ];

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->submitHomework($authCode, $data);
    }

    /**
     * Mark homework as done (completed) without submission
     */
    public function markHomeworkAsDone(Request $request)
    {
        $authCode = $request->input('auth_code');
        $detailId = $request->input('detail_id');

        if (!$authCode || !$detailId) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code and detail ID are required'
            ], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->markHomeworkAsDone($authCode, $detailId);
    }

    /**
     * Upload file for homework submission
     */
    public function uploadHomeworkFile(Request $request)
    {
        $authCode = $request->input('auth_code');

        if (!$authCode) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code is required'
            ], 400);
        }

        // Validate file upload - use Validator for API to avoid redirects
        $validator = \Validator::make($request->all(), [
            'file' => 'required|file|max:10240', // 10MB max
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 400);
        }

        $file = $request->file('file');

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->uploadHomeworkFile($authCode, $file);
    }

    /**
     * Update/resubmit homework after teacher feedback
     */
    public function updateHomeworkSubmission(Request $request)
    {
        $authCode = $request->input('auth_code');

        if (!$authCode) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code is required'
            ], 400);
        }

        // Validate required fields
        $request->validate([
            'detail_id' => 'required|integer',
            'reply_file' => 'nullable|string',
            'reply_data' => 'nullable|string'
        ]);

        // At least one submission field is required for update
        if (empty($request->input('reply_file')) && empty($request->input('reply_data'))) {
            return response()->json([
                'success' => false,
                'message' => 'At least one submission (file or data) is required for update'
            ], 400);
        }

        $data = [
            'detail_id' => $request->input('detail_id'),
            'reply_file' => $request->input('reply_file'),
            'reply_data' => $request->input('reply_data')
        ];

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->updateHomeworkSubmission($authCode, $data);
    }

    /**
     * Get teacher's homework list
     */
    public function getTeacherHomeworkList(Request $request)
    {
        $authCode = $request->query('auth_code');

        if (!$authCode) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code is required'
            ], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getTeacherHomeworkList($authCode);
    }

    /**
     * Get homework details with student submissions for teacher review
     */
    public function getTeacherHomeworkDetails(Request $request)
    {
        $authCode = $request->query('auth_code');
        $homeworkId = $request->query('homework_id');

        if (!$authCode || !$homeworkId) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code and homework ID are required'
            ], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getTeacherHomeworkDetails($authCode, $homeworkId);
    }

    /**
     * Provide teacher feedback on student homework submission
     */
    public function provideHomeworkFeedback(Request $request)
    {
        $authCode = $request->input('auth_code');

        if (!$authCode) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code is required'
            ], 400);
        }

        // Validate required fields
        $request->validate([
            'detail_id' => 'required|integer',
            'comment' => 'required|string|min:1'
        ]);

        $data = [
            'detail_id' => $request->input('detail_id'),
            'comment' => $request->input('comment'),
            'approval_status' => $request->input('approval_status') // Optional: approved, rejected
        ];

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->provideHomeworkFeedback($authCode, $data);
    }

    /**
     * Approve or reject homework submission
     */
    public function reviewHomeworkSubmission(Request $request)
    {
        $authCode = $request->input('auth_code');

        if (!$authCode) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code is required'
            ], 400);
        }

        // Validate required fields
        $request->validate([
            'detail_id' => 'required|integer',
            'action' => 'required|string|in:approve,reject'
        ]);

        // Comment is required for rejection
        if ($request->input('action') === 'reject') {
            $request->validate([
                'comment' => 'required|string|min:1'
            ]);
        }

        $data = [
            'detail_id' => $request->input('detail_id'),
            'action' => $request->input('action'),
            'comment' => $request->input('comment')
        ];

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->reviewHomeworkSubmission($authCode, $data);
    }

    /**
     * Create new homework assignment
     */
    public function createHomeworkAssignment(Request $request)
    {
        $authCode = $request->input('auth_code');

        if (!$authCode) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code is required'
            ], 400);
        }

        // Validate required fields
        $request->validate([
            'title' => 'required|string|min:1',
            'grade_id' => 'required|integer',
            'students' => 'required|array|min:1',
            'students.*' => 'integer',
            'deadline' => 'required|date',
            'homework_data' => 'nullable|string',
            'homework_files' => 'nullable|array',
            'homework_files.*' => 'file|max:10240', // 10MB per file
            'homework_video_links' => 'nullable|string',
            'create_google_drive_folder' => 'nullable|boolean'
        ]);

        $data = [
            'title' => $request->input('title'),
            'grade_id' => $request->input('grade_id'),
            'students' => $request->input('students'),
            'deadline' => $request->input('deadline'),
            'homework_data' => $request->input('homework_data'),
            'homework_files' => $request->input('homework_files'),
            'homework_video_links' => $request->input('homework_video_links')
        ];

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->createHomeworkAssignment($authCode, $data);
    }

    /**
     * Close homework assignment(s) - marks homework as inactive instead of deleting
     */
    public function closeHomeworkAssignment(Request $request)
    {
        $authCode = $request->input('auth_code');
        $homeworkId = $request->input('homework_id');
        $homeworkIds = $request->input('homework_ids');

        if (!$authCode) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code is required'
            ], 400);
        }

        // Support both single and multiple closing
        $idsToClose = null;
        if ($homeworkIds && is_array($homeworkIds)) {
            // Multiple closing
            $idsToClose = $homeworkIds;
        } elseif ($homeworkId) {
            // Single closing
            $idsToClose = $homeworkId;
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Either homework_id or homework_ids array is required'
            ], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->closeHomeworkAssignment($authCode, $idsToClose);
    }

    /**
     * Get teacher's classes and students for homework assignment
     */
    public function getTeacherClassesForHomework(Request $request)
    {
        $authCode = $request->query('auth_code');

        if (!$authCode) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code is required'
            ], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getTeacherClassesForHomework($authCode);
    }

    /**
     * Get teacher's classes and students for attendance taking
     */
    public function getTeacherClassesForAttendance(Request $request)
    {
        $authCode = $request->query('auth_code');

        if (!$authCode) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code is required'
            ], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getTeacherClassesForAttendance($authCode);
    }

    /**
     * Get homeroom teacher's assigned classrooms
     */
    public function getHomeroomClassrooms(Request $request)
    {
        $authCode = $request->query('auth_code');

        if (!$authCode) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code is required'
            ], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getHomeroomClassrooms($authCode);
    }

    /**
     * Get students in a specific homeroom classroom
     */
    public function getHomeroomStudents(Request $request)
    {
        $authCode = $request->query('auth_code');
        $classroomId = $request->query('classroom_id');

        if (!$authCode || !$classroomId) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code and classroom ID are required'
            ], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getHomeroomStudents($authCode, $classroomId);
    }

    /**
     * Get homeroom attendance summary for a specific date
     */
    public function getHomeroomAttendance(Request $request)
    {
        $authCode = $request->query('auth_code');
        $classroomId = $request->query('classroom_id');
        $date = $request->query('date'); // Optional, defaults to today

        if (!$authCode || !$classroomId) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code and classroom ID are required'
            ], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getHomeroomAttendance($authCode, $classroomId, $date);
    }

    /**
     * Get homeroom discipline records summary
     */
    public function getHomeroomDiscipline(Request $request)
    {
        $authCode = $request->query('auth_code');
        $classroomId = $request->query('classroom_id');
        $startDate = $request->query('start_date'); // Optional
        $endDate = $request->query('end_date'); // Optional

        if (!$authCode || !$classroomId) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code and classroom ID are required'
            ], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getHomeroomDiscipline($authCode, $classroomId, $startDate, $endDate);
    }

    /**
     * Get detailed student profile for homeroom teacher
     */
    public function getHomeroomStudentProfile(Request $request)
    {
        $authCode = $request->query('auth_code');
        $studentId = $request->query('student_id');

        if (!$authCode || !$studentId) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code and student ID are required'
            ], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getHomeroomStudentProfile($authCode, $studentId);
    }

    /**
     * ========================================
     * MOBILE MESSAGING API METHODS
     * ========================================
     */

    /**
     * Get conversations list for mobile user
     */
    public function getConversations(Request $request) {
        $authCode = $request->input('authCode');

        if (!$authCode) {
            return response()->json(['error' => 'authCode is required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getConversations($authCode);
    }

    /**
     * Get messages in a conversation
     */
    public function getConversationMessages(Request $request) {
        $authCode = $request->input('authCode');
        $conversationUuid = $request->input('conversation_uuid');
        $page = $request->input('page', 1);
        $limit = $request->input('limit', 50);

        if (!$authCode || !$conversationUuid) {
            return response()->json(['error' => 'authCode and conversation_uuid are required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getConversationMessages($authCode, $conversationUuid, $page, $limit);
    }

    /**
     * Send a message to a conversation
     */
    public function sendMessage(Request $request) {
        $authCode = $request->input('authCode');

        if (!$authCode) {
            return response()->json(['error' => 'authCode is required'], 400);
        }

        $data = $request->only([
            'conversation_uuid',
            'message_content',
            'message_type',
            'attachment_url'
        ]);

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->sendMessage($authCode, $data);
    }

    /**
     * Create a new conversation
     */
    public function createConversation(Request $request) {
        $authCode = $request->input('authCode');

        if (!$authCode) {
            return response()->json(['error' => 'authCode is required'], 400);
        }

        $data = $request->only([
            'topic',
            'members'
        ]);

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->createConversation($authCode, $data);
    }

    /**
     * Get available users for messaging - DEPRECATED
     * Use getAvailableUsersForStudent() or getAvailableUsersForStaff() instead
     */
    public function getAvailableUsers(Request $request) {
        $authCode = $request->input('authCode');
        $userType = $request->input('user_type');

        if (!$authCode) {
            return response()->json(['error' => 'authCode is required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getAvailableUsers($authCode, $userType);
    }

    /**
     * Get available users for messaging - STUDENT VERSION (Restricted)
     *
     * Students can ONLY message:
     * - Their homeroom teacher
     * - Teachers who actually teach them (subject teachers)
     * - Head of section/department
     * - Librarian
     * - Their direct classmates (same classroom)
     *
     * Students CANNOT message:
     * - Other students from different classes
     * - Teachers who don't teach them
     * - Random staff members
     */
    public function getAvailableUsersForStudent(Request $request) {
        $authCode = $request->input('authCode');
        $userType = $request->input('user_type');

        if (!$authCode) {
            return response()->json(['error' => 'authCode is required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getAvailableUsersForStudent($authCode, $userType);
    }

    /**
     * Get available users for messaging - STAFF VERSION (Role-Based Access)
     *
     * Staff access is based on their role:
     * - HEAD OF SCHOOL: Can message all students, all staff, all parents in branch
     * - HEAD OF SECTION/DEPARTMENT: Can message all students in section, all staff, parents of section students
     * - HOMEROOM TEACHER: Can message their homeroom students and their parents only
     * - SUBJECT TEACHER: Can message only students who take their subjects
     * - GENERAL STAFF: Can message only other staff members
     */
    public function getAvailableUsersForStaff(Request $request) {
        $authCode = $request->input('authCode');
        $userType = $request->input('user_type');

        if (!$authCode) {
            return response()->json(['error' => 'authCode is required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getAvailableUsersForStaff($authCode, $userType);
    }

    /**
     * Search conversations and messages
     */
    public function searchMessages(Request $request) {
        $authCode = $request->input('authCode');
        $query = $request->input('query');
        $type = $request->input('type', 'all');

        if (!$authCode || !$query) {
            return response()->json(['error' => 'authCode and query are required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->searchMessages($authCode, $query, $type);
    }

    /**
     * Mark messages as read in a conversation
     */
    public function markMessagesAsRead(Request $request) {
        $authCode = $request->input('authCode');
        $conversationUuid = $request->input('conversation_uuid');

        if (!$authCode || !$conversationUuid) {
            return response()->json(['error' => 'authCode and conversation_uuid are required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->markMessagesAsRead($authCode, $conversationUuid);
    }

    /**
     * Mark a specific message as read
     */
    public function markMessageAsRead(Request $request) {
        $authCode = $request->input('authCode');
        $messageId = $request->input('message_id');

        if (!$authCode || !$messageId) {
            return response()->json(['error' => 'authCode and message_id are required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->markMessageAsRead($authCode, $messageId);
    }

    /**
     * Get conversation members grouped by user type
     */
    public function getConversationMembers(Request $request) {
        $authCode = $request->input('authCode');
        $conversationUuid = $request->input('conversation_uuid');

        if (!$authCode || !$conversationUuid) {
            return response()->json(['error' => 'authCode and conversation_uuid are required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getConversationMembers($authCode, $conversationUuid);
    }

    /**
     * Delete a conversation (only creator can delete)
     */
    public function deleteConversation(Request $request) {
        $authCode = $request->input('authCode');
        $conversationUuid = $request->input('conversation_uuid');

        if (!$authCode || !$conversationUuid) {
            return response()->json(['error' => 'authCode and conversation_uuid are required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->deleteConversation($authCode, $conversationUuid);
    }

    /**
     * Leave a conversation (remove user from conversation)
     */
    public function leaveConversation(Request $request) {
        $authCode = $request->input('authCode');
        $conversationUuid = $request->input('conversation_uuid');

        if (!$authCode || !$conversationUuid) {
            return response()->json(['error' => 'authCode and conversation_uuid are required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->leaveConversation($authCode, $conversationUuid);
    }

    /**
     * Delete a specific message (only sender can delete their own messages)
     */
    public function deleteMessage(Request $request) {
        $authCode = $request->input('authCode');
        $messageId = $request->input('message_id');

        if (!$authCode || !$messageId) {
            return response()->json(['error' => 'authCode and message_id are required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->deleteMessage($authCode, $messageId);
    }

    /**
     * Clear message text (replace with "[Message Deleted]")
     */
    public function clearMessageText(Request $request) {
        $authCode = $request->input('authCode');
        $messageId = $request->input('message_id');

        if (!$authCode || !$messageId) {
            return response()->json(['error' => 'authCode and message_id are required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->clearMessageText($authCode, $messageId);
    }

    /**
     * Admin delete message (staff only)
     */
    public function adminDeleteMessage(Request $request) {
        $authCode = $request->input('authCode');
        $messageId = $request->input('message_id');

        if (!$authCode || !$messageId) {
            return response()->json(['error' => 'authCode and message_id are required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->adminDeleteMessage($authCode, $messageId);
    }

    /**
     * Bulk delete messages
     */
    public function bulkDeleteMessages(Request $request) {
        $authCode = $request->input('authCode');
        $messageIds = $request->input('message_ids');
        $deleteType = $request->input('delete_type', 'soft'); // 'soft' or 'hard'

        if (!$authCode || !$messageIds) {
            return response()->json(['error' => 'authCode and message_ids are required'], 400);
        }

        if (!in_array($deleteType, ['soft', 'hard'])) {
            return response()->json(['error' => 'delete_type must be either "soft" or "hard"'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->bulkDeleteMessages($authCode, $messageIds, $deleteType);
    }

    /**
     * Edit message (1-minute time limit)
     */
    public function editMessage(Request $request) {
        $authCode = $request->input('authCode');
        $messageId = $request->input('message_id');
        $newContent = $request->input('new_content');

        if (!$authCode || !$messageId || !$newContent) {
            return response()->json(['error' => 'authCode, message_id, and new_content are required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->editMessage($authCode, $messageId, $newContent);
    }

    /**
     * Upload attachment for message
     */
    public function uploadMessageAttachment(Request $request) {
        $authCode = $request->input('authCode');
        $file = $request->file('attachment');

        if (!$authCode) {
            return response()->json(['error' => 'authCode is required'], 400);
        }

        if (!$file) {
            return response()->json(['error' => 'attachment file is required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->uploadMessageAttachment($authCode, $file);
    }

    // ========================================
    // HEALTH MANAGEMENT API METHODS
    // ========================================

    /**
     * Get student health records for mobile
     */
    public function getStudentHealthRecords(Request $request) {
        $authCode = $request->input('authCode');

        if (!$authCode) {
            return response()->json(['error' => 'authCode is required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getStudentHealthRecords($authCode);
    }

    /**
     * Get student health information for mobile
     */
    public function getStudentHealthInfo(Request $request) {
        $authCode = $request->input('authCode');

        if (!$authCode) {
            return response()->json(['error' => 'authCode is required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getStudentHealthInfo($authCode);
    }

    /**
     * Get teacher health data for mobile (staff only)
     */
    public function getTeacherHealthData(Request $request) {
        $authCode = $request->input('authCode');

        if (!$authCode) {
            return response()->json(['error' => 'authCode is required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getTeacherHealthData($authCode);
    }

    /**
     * Create student health record for mobile (staff only)
     */
    public function createStudentHealthRecord(MobileStudentHealthRecordRequest $request) {
        $data = $request->validated();
        $authCode = $data['authCode'];

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->createStudentHealthRecord($authCode, $data);
    }

    /**
     * Create staff health record for mobile (staff only)
     */
    public function createStaffHealthRecord(MobileStaffHealthRecordRequest $request) {
        $data = $request->validated();
        $authCode = $data['authCode'];

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->createStaffHealthRecord($authCode, $data);
    }

    /**
     * Create guest health record for mobile (staff only)
     */
    public function createGuestHealthRecord(MobileGuestHealthRecordRequest $request) {
        $data = $request->validated();
        $authCode = $data['authCode'];

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->createGuestHealthRecord($authCode, $data);
    }

    /**
     * Update student health information for mobile (staff only)
     */
    public function updateStudentHealthInfo(MobileStudentHealthInfoRequest $request) {
        $data = $request->validated();
        $authCode = $data['authCode'];

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->updateStudentHealthInfo($authCode, $data);
    }

    /**
     * Delete health record for mobile (staff only)
     */
    public function deleteHealthRecord(Request $request) {
        $authCode = $request->input('authCode');
        $recordType = $request->input('record_type');
        $recordId = $request->input('record_id');

        if (!$authCode) {
            return response()->json(['error' => 'authCode is required'], 400);
        }

        if (!$recordType || !$recordId) {
            return response()->json(['error' => 'record_type and record_id are required'], 400);
        }

        if (!in_array($recordType, ['student', 'staff', 'guest'])) {
            return response()->json(['error' => 'Invalid record_type. Must be student, staff, or guest'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->deleteHealthRecord($authCode, $recordType, $recordId);
    }

    /**
     * Get health lookup data for mobile (injuries, actions, medications)
     */
    public function getHealthLookupData(Request $request) {
        $authCode = $request->input('authCode');

        if (!$authCode) {
            return response()->json(['error' => 'authCode is required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getHealthLookupData($authCode);
    }

    /**
     * Create student measurement record for mobile (staff only)
     */
    public function createStudentMeasurement(Request $request) {
        $authCode = $request->input('authCode');
        $data = $request->all();

        if (!$authCode) {
            return response()->json(['error' => 'authCode is required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->createStudentMeasurement($authCode, $data);
    }

    /**
     * Delete student measurement record for mobile (staff only)
     */
    public function deleteStudentMeasurement(Request $request) {
        $authCode = $request->input('authCode');
        $measurementId = $request->input('measurement_id');

        if (!$authCode) {
            return response()->json(['error' => 'authCode is required'], 400);
        }

        if (!$measurementId) {
            return response()->json(['error' => 'measurement_id is required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->deleteStudentMeasurement($authCode, $measurementId);
    }

    /**
     * Logout user from mobile device
     */
    public function logout(Request $request) {
        $authCode = $request->input('authCode');

        if (!$authCode) {
            return response()->json(['error' => 'authCode is required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->logoutUser($authCode);
    }

    /**
     * Logout user from all devices (admin/security function)
     */
    public function logoutFromAllDevices(Request $request) {
        $userId = $request->input('userId');

        if (!$userId) {
            return response()->json(['error' => 'userId is required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->logoutUserFromAllDevices($userId);
    }

    /**
     * Update last_login timestamp when user opens the app
     */
    public function updateLastLogin(Request $request) {
        $authCode = $request->input('authCode');

        if (!$authCode) {
            return response()->json(['error' => 'authCode is required'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->updateLastLogin($authCode);
    }

    /**
     * Clean up mobile devices with last_login older than specified days
     */
    public function cleanupOldDevices(Request $request) {
        $daysOld = $request->input('days_old', 30); // Default to 30 days

        // Validate days_old parameter
        if (!is_numeric($daysOld) || $daysOld < 1) {
            return response()->json(['error' => 'days_old must be a positive number'], 400);
        }

        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->cleanupOldDevices($daysOld);
    }

    /**
     * Get statistics about mobile devices by age
     */
    public function getDeviceStatistics(Request $request) {
        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getDeviceStatistics();
    }

    // ========================================
    // GOOGLE DRIVE WORKSPACE MOBILE API ENDPOINTS
    // ========================================

    /**
     * Get workspace structure for mobile
     */
    public function getWorkspaceStructure(Request $request) {
        try {
            $authCode = $request->input('authCode') ?? $request->input('authcode') ?? $request->input('auth_code');

            if (!$authCode) {
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication code is required',
                    'error_code' => 'AUTH_CODE_REQUIRED'
                ], 401);
            }

            $mobileApiRepository = new MobileApiRepository();
            return $mobileApiRepository->getWorkspaceStructure($authCode);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get workspace structure',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get folder contents for mobile
     */
    public function getWorkspaceFolderContents(Request $request) {
        try {
            $authCode = $request->input('authCode') ?? $request->input('authcode') ?? $request->input('auth_code');
            $folderId = $request->input('folder_id');

            if (!$authCode) {
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication code is required',
                    'error_code' => 'AUTH_CODE_REQUIRED'
                ], 401);
            }

            if (!$folderId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Folder ID is required',
                    'error_code' => 'FOLDER_ID_REQUIRED'
                ], 400);
            }

            $mobileApiRepository = new MobileApiRepository();
            return $mobileApiRepository->getFolderContents($authCode, $folderId);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get folder contents',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upload file to workspace via mobile
     */
    public function uploadWorkspaceFile(Request $request) {
        try {
            $authCode = $request->input('authCode') ?? $request->input('authcode') ?? $request->input('auth_code');
            $folderId = $request->input('folder_id');
            $description = $request->input('description');
            $file = $request->file('file');

            if (!$authCode) {
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication code is required',
                    'error_code' => 'AUTH_CODE_REQUIRED'
                ], 401);
            }

            if (!$folderId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Folder ID is required',
                    'error_code' => 'FOLDER_ID_REQUIRED'
                ], 400);
            }

            if (!$file) {
                return response()->json([
                    'success' => false,
                    'message' => 'File is required',
                    'error_code' => 'FILE_REQUIRED'
                ], 400);
            }

            $mobileApiRepository = new MobileApiRepository();
            return $mobileApiRepository->uploadWorkspaceFile($authCode, $file, $folderId, $description);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload file',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create folder in workspace via mobile
     */
    public function createWorkspaceFolder(Request $request) {
        try {
            $authCode = $request->input('authCode') ?? $request->input('authcode') ?? $request->input('auth_code');
            $folderName = $request->input('folder_name');
            $parentFolderId = $request->input('parent_folder_id');
            $description = $request->input('description');

            if (!$authCode) {
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication code is required',
                    'error_code' => 'AUTH_CODE_REQUIRED'
                ], 401);
            }

            if (!$folderName) {
                return response()->json([
                    'success' => false,
                    'message' => 'Folder name is required',
                    'error_code' => 'FOLDER_NAME_REQUIRED'
                ], 400);
            }

            if (!$parentFolderId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Parent folder ID is required',
                    'error_code' => 'PARENT_FOLDER_ID_REQUIRED'
                ], 400);
            }

            $mobileApiRepository = new MobileApiRepository();
            return $mobileApiRepository->createWorkspaceFolder($authCode, $folderName, $parentFolderId, $description);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create folder',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Search files in workspace via mobile
     */
    public function searchWorkspaceFiles(Request $request) {
        try {
            $authCode = $request->input('authCode') ?? $request->input('authcode') ?? $request->input('auth_code');
            $query = $request->input('query');

            if (!$authCode) {
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication code is required',
                    'error_code' => 'AUTH_CODE_REQUIRED'
                ], 401);
            }

            if (!$query) {
                return response()->json([
                    'success' => false,
                    'message' => 'Search query is required',
                    'error_code' => 'QUERY_REQUIRED'
                ], 400);
            }

            $mobileApiRepository = new MobileApiRepository();
            return $mobileApiRepository->searchWorkspaceFiles($authCode, $query);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Search failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get recent files from workspace via mobile
     */
    public function getRecentWorkspaceFiles(Request $request) {
        try {
            $authCode = $request->input('authCode') ?? $request->input('authcode') ?? $request->input('auth_code');
            $limit = $request->input('limit', 20);

            if (!$authCode) {
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication code is required',
                    'error_code' => 'AUTH_CODE_REQUIRED'
                ], 401);
            }

            $mobileApiRepository = new MobileApiRepository();
            return $mobileApiRepository->getRecentWorkspaceFiles($authCode, $limit);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get recent files',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get workspace statistics via mobile
     */
    public function getWorkspaceStats(Request $request) {
        try {
            $authCode = $request->input('authCode') ?? $request->input('authcode') ?? $request->input('auth_code');

            if (!$authCode) {
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication code is required',
                    'error_code' => 'AUTH_CODE_REQUIRED'
                ], 401);
            }

            $mobileApiRepository = new MobileApiRepository();
            return $mobileApiRepository->getWorkspaceStats($authCode);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get workspace statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete file or folder from workspace via mobile (staff only)
     */
    public function deleteWorkspaceItem(Request $request) {
        try {
            $authCode = $request->input('authCode') ?? $request->input('authcode') ?? $request->input('auth_code');
            $itemId = $request->input('item_id');
            $isFolder = $request->input('is_folder', false);

            if (!$authCode) {
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication code is required',
                    'error_code' => 'AUTH_CODE_REQUIRED'
                ], 401);
            }

            if (!$itemId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Item ID is required',
                    'error_code' => 'ITEM_ID_REQUIRED'
                ], 400);
            }

            $mobileApiRepository = new MobileApiRepository();
            return $mobileApiRepository->deleteWorkspaceItem($authCode, $itemId, $isFolder);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete item',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get About Us information for mobile app
     */
    public function getAboutUsData(Request $request) {
        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getAboutUsData();
    }

    /**
     * Get Contacts information for mobile app
     */
    public function getContactsData(Request $request) {
        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getContactsData();
    }

    /**
     * Get FAQ information for mobile app
     */
    public function getFAQData(Request $request) {
        $mobileApiRepository = new MobileApiRepository();
        return $mobileApiRepository->getFAQData();
    }

    /**
     * Get calendar data with Google Calendar integration
     */
    public function getCalendarData(Request $request) {
        try {
            $authCode = $request->input('authCode') ?? $request->input('auth_code');
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');

            if (!$authCode) {
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication code is required (authCode or auth_code parameter)',
                    'error_code' => 'AUTH_CODE_REQUIRED'
                ], 401);
            }

            $mobileApiRepository = new MobileApiRepository();
            return $mobileApiRepository->getCalendarData($authCode, $startDate, $endDate);

        } catch (\Exception $e) {
            \Log::error('Calendar data API error', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);
            return response()->json(['error' => 'Failed to retrieve calendar data'], 500);
        }
    }

    /**
     * Get upcoming calendar events (next 30 days by default)
     */
    public function getUpcomingCalendarEvents(Request $request) {
        try {
            $authCode = $request->input('authCode') ?? $request->input('auth_code');
            $days = $request->input('days', 30);

            if (!$authCode) {
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication code is required (authCode or auth_code parameter)',
                    'error_code' => 'AUTH_CODE_REQUIRED'
                ], 401);
            }

            // Validate days parameter
            if (!is_numeric($days) || $days < 1 || $days > 365) {
                return response()->json([
                    'success' => false,
                    'message' => 'Days parameter must be a number between 1 and 365',
                    'error_code' => 'INVALID_DAYS_PARAMETER'
                ], 400);
            }

            $mobileApiRepository = new MobileApiRepository();
            return $mobileApiRepository->getUpcomingCalendarEvents($authCode, (int)$days);

        } catch (\Exception $e) {
            \Log::error('Upcoming calendar events API error', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);
            return response()->json(['error' => 'Failed to retrieve upcoming calendar events'], 500);
        }
    }

    /**
     * Get calendar events for a specific month
     */
    public function getMonthlyCalendarEvents(Request $request) {
        try {
            $authCode = $request->input('authCode') ?? $request->input('auth_code');
            $year = $request->input('year');
            $month = $request->input('month');

            if (!$authCode) {
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication code is required (authCode or auth_code parameter)',
                    'error_code' => 'AUTH_CODE_REQUIRED'
                ], 401);
            }

            // Validate year and month if provided
            if ($year && (!is_numeric($year) || $year < 2020 || $year > 2030)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Year must be a number between 2020 and 2030',
                    'error_code' => 'INVALID_YEAR_PARAMETER'
                ], 400);
            }

            if ($month && (!is_numeric($month) || $month < 1 || $month > 12)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Month must be a number between 1 and 12',
                    'error_code' => 'INVALID_MONTH_PARAMETER'
                ], 400);
            }

            $mobileApiRepository = new MobileApiRepository();
            return $mobileApiRepository->getMonthlyCalendarEvents($authCode, $year ? (int)$year : null, $month ? (int)$month : null);

        } catch (\Exception $e) {
            \Log::error('Monthly calendar events API error', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);
            return response()->json(['error' => 'Failed to retrieve monthly calendar events'], 500);
        }
    }

    /**
     * Test Google Calendar connection for a branch (staff only)
     */
    public function testGoogleCalendarConnection(Request $request) {
        try {
            $authCode = $request->input('authCode') ?? $request->input('auth_code');
            $branchId = $request->input('branch_id');

            if (!$authCode) {
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication code is required (authCode or auth_code parameter)',
                    'error_code' => 'AUTH_CODE_REQUIRED'
                ], 401);
            }

            if (!$branchId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Branch ID is required (branch_id parameter)',
                    'error_code' => 'BRANCH_ID_REQUIRED'
                ], 400);
            }

            if (!is_numeric($branchId)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Branch ID must be a valid number',
                    'error_code' => 'INVALID_BRANCH_ID'
                ], 400);
            }

            $mobileApiRepository = new MobileApiRepository();
            return $mobileApiRepository->testGoogleCalendarConnection($authCode, (int)$branchId);

        } catch (\Exception $e) {
            \Log::error('Google Calendar connection test API error', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);
            return response()->json(['error' => 'Failed to test Google Calendar connection'], 500);
        }
    }

    /**
     * Get personalized calendar events for a user (homework due dates, exams, birthdays)
     */
    public function getPersonalCalendarEvents(Request $request) {
        try {
            $authCode = $request->input('authCode') ?? $request->input('auth_code');
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');

            if (!$authCode) {
                return response()->json([
                    'success' => false,
                    'error' => 'Authentication code is required',
                    'error_code' => 'MISSING_AUTH_CODE'
                ], 400);
            }

            // Set default date range if not provided (next 30 days)
            if (!$startDate) {
                $startDate = date('Y-m-d');
            }
            if (!$endDate) {
                $endDate = date('Y-m-d', strtotime('+30 days'));
            }

            // Validate date format
            if (!strtotime($startDate) || !strtotime($endDate)) {
                return response()->json([
                    'success' => false,
                    'error' => 'Invalid date format. Use YYYY-MM-DD',
                    'error_code' => 'INVALID_DATE_FORMAT'
                ], 400);
            }

            $mobileApiRepository = new MobileApiRepository();
            return $mobileApiRepository->getPersonalCalendarEvents($authCode, $startDate, $endDate);

        } catch (\Exception $e) {
            \Log::error('Personal Calendar API error', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);
            return response()->json(['error' => 'Failed to retrieve personal calendar events'], 500);
        }
    }

    /**
     * Create homework folder for teacher
     */
    public function createHomeworkFolder(Request $request)
    {
        $authCode = $request->input('auth_code');

        if (!$authCode) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code is required'
            ], 400);
        }

        // Validate required fields - use Validator for API to avoid redirects
        $validator = \Validator::make($request->all(), [
            'folder_name' => 'required|string|min:1|max:255',
            'description' => 'nullable|string|max:1000',
            'assignment_type' => 'required|in:class,student,mixed',
            'assigned_classes' => 'required_if:assignment_type,class,mixed|array',
            'assigned_classes.*' => 'integer',
            'assigned_students' => 'required_if:assignment_type,student,mixed|array',
            'assigned_students.*' => 'integer',
            'homework_id' => 'nullable|integer|exists:academic_homework,homework_id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            $mobileApi = new MobileApiRepository();
            $authResult = $mobileApi->checkAuthCode($authCode);

            if (!$authResult['success']) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid authentication code'
                ], 401);
            }

            $teacherId = $authResult['user_id'];
            $userType = $authResult['user_type'];

            if ($userType !== 'staff') {
                return response()->json([
                    'success' => false,
                    'message' => 'Only staff members can create homework folders'
                ], 403);
            }

            // Get branch ID for permission checking
            $academicHelper = new \App\Library\Helper\AcademicHelper();
            $branchId = $academicHelper->mobileCurrentBranch($teacherId);

            if (!$branchId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Branch information not found for teacher'
                ], 404);
            }

            // Check if academic year is configured for this branch
            $academicYearId = $academicHelper->branchAcademicYear($branchId);
            if (!$academicYearId || !is_numeric($academicYearId)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Academic year settings are not configured for your branch. Please contact the administrator to set up a default academic semester.',
                    'error_details' => [
                        'branch_id' => $branchId,
                        'teacher_id' => $teacherId,
                        'issue' => 'No default academic semester found for branch'
                    ]
                ], 422);
            }

            // Check if user has homework Google Drive permissions
            if (!$this->checkHomeworkGoogleDriveAccess($teacherId, $branchId, 'write')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied. You do not have permission to create homework folders.'
                ], 403);
            }

            // Check if Google Drive is available
            if (!$this->driveRepository) {
                return response()->json([
                    'success' => false,
                    'message' => 'Google Drive service is not available'
                ], 503);
            }

            // Create homework folder
            $result = $this->driveRepository->createHomeworkFolder(
                $teacherId,
                $request->input('folder_name'),
                $request->input('description'),
                $request->input('assignment_type'),
                $request->input('assigned_classes', []),
                $request->input('assigned_students', [])
            );

            if ($result['success']) {
                $responseData = [
                    'folder_id' => $result['homework_folder']->id,
                    'google_drive_folder_id' => $result['drive_folder']['id'],
                    'folder_name' => $result['homework_folder']->folder_name,
                    'assignment_type' => $result['homework_folder']->assignment_type,
                    'web_view_link' => $result['drive_folder']['web_view_link'] ?? null
                ];

                // If homework_id is provided, update the homework record with Google Drive folder info
                $homeworkId = $request->input('homework_id');
                if ($homeworkId) {
                    try {
                        $homework = \App\Models\Homework::find($homeworkId);
                        if ($homework && $homework->user_id == $teacherId) {
                            $homework->google_drive_folder_id = $result['drive_folder']['id'];
                            $homework->save();

                            $responseData['homework_updated'] = true;
                            $responseData['homework_id'] = $homeworkId;

                            \Log::info('Homework record updated with Google Drive folder', [
                                'homework_id' => $homeworkId,
                                'google_drive_folder_id' => $result['drive_folder']['id'],
                                'teacher_id' => $teacherId
                            ]);
                        } else {
                            \Log::warning('Homework not found or access denied for Google Drive folder update', [
                                'homework_id' => $homeworkId,
                                'teacher_id' => $teacherId
                            ]);
                        }
                    } catch (\Exception $e) {
                        \Log::error('Error updating homework with Google Drive folder', [
                            'homework_id' => $homeworkId,
                            'error' => $e->getMessage()
                        ]);
                    }
                }

                return response()->json([
                    'success' => true,
                    'message' => 'Homework folder created successfully',
                    'data' => $responseData
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to create homework folder',
                    'error' => $result['error']
                ], 500);
            }

        } catch (\Exception $e) {
            \Log::error('Create homework folder error', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to create homework folder'
            ], 500);
        }
    }

    /**
     * Diagnostic endpoint to check academic year configuration for a teacher
     */
    public function checkAcademicYearConfig(Request $request)
    {
        try {
            $authCode = $request->input('auth_code');

            if (!$authCode) {
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication code is required'
                ], 400);
            }

            // Verify authentication
            $device = MobileDevice::where('auth_code', $authCode)->first();
            if (!$device || $device->user_type !== 'staff') {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid authentication code or insufficient permissions'
                ], 401);
            }

            $teacherId = $device->student_id;
            $academicHelper = new \App\Library\Helper\AcademicHelper();
            $branchId = $academicHelper->mobileCurrentBranch($teacherId);

            // Get academic semester info
            $academicSemester = null;
            $academicYearId = null;

            if ($branchId) {
                $academicSemester = \App\Models\AcademicSemester::where('is_default', 1)
                    ->where('branch_id', $branchId)
                    ->first();

                if ($academicSemester) {
                    $academicYearId = $academicSemester->academic_year_id;
                }
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'teacher_id' => $teacherId,
                    'branch_id' => $branchId,
                    'academic_year_id' => $academicYearId,
                    'has_default_semester' => $academicSemester !== null,
                    'semester_info' => $academicSemester ? [
                        'semester_id' => $academicSemester->semester_id,
                        'academic_semester' => $academicSemester->academic_semester,
                        'start_date' => $academicSemester->start_date,
                        'end_date' => $academicSemester->end_date,
                        'is_default' => $academicSemester->is_default
                    ] : null,
                    'diagnosis' => [
                        'branch_found' => $branchId !== null,
                        'default_semester_exists' => $academicSemester !== null,
                        'academic_year_valid' => $academicYearId && is_numeric($academicYearId),
                        'ready_for_homework_creation' => $branchId && $academicSemester && $academicYearId && is_numeric($academicYearId)
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Diagnostic check failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get teacher's homework folders organized by classes and subjects
     */
    public function getTeacherHomeworkFolders(Request $request)
    {
        $authCode = $request->input('auth_code');

        if (!$authCode) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code is required'
            ], 400);
        }

        try {
            $mobileApi = new MobileApiRepository();
            $authResult = $mobileApi->checkAuthCode($authCode);

            if (!$authResult['success']) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid authentication code'
                ], 401);
            }

            $teacherId = $authResult['user_id'];
            $userType = $authResult['user_type'];

            if ($userType !== 'staff') {
                return response()->json([
                    'success' => false,
                    'message' => 'Only staff members can access homework folders'
                ], 403);
            }

            // Get branch ID for permission checking
            $academicHelper = new \App\Library\Helper\AcademicHelper();
            $branchId = $academicHelper->mobileCurrentBranch($teacherId);

            // Check if teacher has homework Google Drive permissions
            if (!$this->checkHomeworkGoogleDriveAccess($teacherId, $branchId, 'read')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied. You do not have permission to access homework folders.'
                ], 403);
            }

            // Check if Google Drive is available
            if (!$this->driveRepository) {
                return response()->json([
                    'success' => false,
                    'message' => 'Google Drive service is not available'
                ], 503);
            }

            // Get teacher's classes and subjects
            $academicHelper = new \App\Library\Helper\AcademicHelper();
            $branchId = $academicHelper->mobileCurrentBranch($teacherId);

            if (!$branchId) {
                Log::error('Branch ID not found for teacher', [
                    'teacher_id' => $teacherId,
                    'auth_code' => $authCode
                ]);
                return response()->json([
                    'success' => false,
                    'error' => 'Branch information not found for teacher'
                ], 404);
            }

            $academicYearId = $academicHelper->branchAcademicYear($branchId);
            if (!$academicYearId || !is_numeric($academicYearId)) {
                Log::error('Academic year not found for branch', [
                    'teacher_id' => $teacherId,
                    'branch_id' => $branchId,
                    'auth_code' => $authCode,
                    'academic_year_result' => $academicYearId
                ]);
                return response()->json([
                    'success' => false,
                    'error' => 'Academic year not found for branch'
                ], 404);
            }

            // Get teacher's homework folders with proper parameters
            $folders = $this->driveRepository->getTeacherHomeworkFolders($teacherId, $branchId, $academicYearId);

            // Get teacher's assigned classes with subjects through timetable
            $teacherClasses = \DB::table('academic_timetable')
                ->leftJoin('academic_elective_grade', 'academic_elective_grade.grade_id', 'academic_timetable.grade_id')
                ->leftJoin('subjects', 'subjects.subject_id', 'academic_timetable.subject_id')
                ->where('academic_timetable.user_id', $teacherId)
                ->where('academic_timetable.academic_year_id', $academicYearId)
                ->where('academic_timetable.branch_id', $branchId)
                ->select(
                    'subjects.subject_id',
                    'subjects.subject_name',
                    'academic_elective_grade.grade_id',
                    'academic_elective_grade.grade_name'
                )
                ->groupBy('subjects.subject_id', 'subjects.subject_name', 'academic_elective_grade.grade_id', 'academic_elective_grade.grade_name')
                ->get()
                ->groupBy('subject_name');

            // Organize folders by subjects and classes
            $organizedData = [];

            foreach ($teacherClasses as $subjectName => $subjectGrades) {
                $subjectData = [
                    'subject_name' => $subjectName,
                    'subject_id' => $subjectGrades->first()->subject_id,
                    'classes' => [],
                    'folders' => []
                ];

                // Add class information
                foreach ($subjectGrades as $grade) {
                    $subjectData['classes'][] = [
                        'grade_id' => $grade->grade_id,
                        'grade_name' => $grade->grade_name
                    ];

                    // Find folders for this grade
                    $gradeFolders = $folders->filter(function($folder) use ($grade) {
                        return in_array($grade->grade_id, $folder->assigned_classes ?? []);
                    });

                    foreach ($gradeFolders as $folder) {
                        // Get files in this folder
                        $folderFiles = $this->getHomeworkFolderFilesList($folder->google_drive_folder_id);

                        // Get assigned students for this folder
                        $assignedStudents = $folder->assignedStudents->map(function($student) {
                            return [
                                'student_id' => $student->id,
                                'student_name' => $student->name
                            ];
                        })->toArray();

                        $subjectData['folders'][] = [
                            'id' => $folder->id,
                            'folder_name' => $folder->folder_name,
                            'description' => $folder->description,
                            'google_drive_folder_id' => $folder->google_drive_folder_id,
                            'assignment_type' => $folder->assignment_type,
                            'assigned_classes' => $folder->assignedGrades->map(function($grade) {
                                return [
                                    'grade_id' => $grade->grade_id,
                                    'grade_name' => $grade->grade_name
                                ];
                            })->toArray(),
                            'assigned_students' => $assignedStudents,
                            'created_at' => $folder->created_at->format('Y-m-d H:i:s'),
                            'is_active' => $folder->is_active,
                            'files' => $folderFiles,
                            'file_count' => count($folderFiles),
                            'student_count' => count($assignedStudents) + $this->getClassStudentCount($folder->assigned_classes ?? [])
                        ];
                    }
                }

                // Remove duplicate folders
                $subjectData['folders'] = collect($subjectData['folders'])
                    ->unique('id')
                    ->values()
                    ->toArray();

                if (!empty($subjectData['folders']) || !empty($subjectData['classes'])) {
                    $organizedData[] = $subjectData;
                }
            }

            // Also include folders not assigned to specific classes (individual assignments)
            $individualFolders = $folders->filter(function($folder) {
                return $folder->assignment_type === 'student' ||
                       ($folder->assignment_type === 'mixed' && empty($folder->assigned_classes));
            });

            if ($individualFolders->isNotEmpty()) {
                $individualFoldersData = [
                    'subject_name' => 'Individual Assignments',
                    'subject_id' => null,
                    'classes' => [],
                    'folders' => []
                ];

                foreach ($individualFolders as $folder) {
                    $folderFiles = $this->getHomeworkFolderFilesList($folder->google_drive_folder_id);

                    $assignedStudents = $folder->assignedStudents->map(function($student) {
                        return [
                            'student_id' => $student->id,
                            'student_name' => $student->name
                        ];
                    })->toArray();

                    $individualFoldersData['folders'][] = [
                        'id' => $folder->id,
                        'folder_name' => $folder->folder_name,
                        'description' => $folder->description,
                        'google_drive_folder_id' => $folder->google_drive_folder_id,
                        'assignment_type' => $folder->assignment_type,
                        'assigned_classes' => [],
                        'assigned_students' => $assignedStudents,
                        'created_at' => $folder->created_at->format('Y-m-d H:i:s'),
                        'is_active' => $folder->is_active,
                        'files' => $folderFiles,
                        'file_count' => count($folderFiles),
                        'student_count' => count($assignedStudents)
                    ];
                }

                if (!empty($individualFoldersData['folders'])) {
                    $organizedData[] = $individualFoldersData;
                }
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'subjects' => $organizedData,
                    'total_subjects' => count($organizedData),
                    'total_folders' => $folders->count(),
                    'summary' => [
                        'class_folders' => $folders->where('assignment_type', 'class')->count(),
                        'individual_folders' => $folders->where('assignment_type', 'student')->count(),
                        'mixed_folders' => $folders->where('assignment_type', 'mixed')->count()
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Get teacher homework folders error', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve homework folders'
            ], 500);
        }
    }

    /**
     * Upload homework file to Google Drive folder
     */
    public function uploadHomeworkFileToFolder(Request $request)
    {
        $authCode = $request->input('auth_code');

        if (!$authCode) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code is required'
            ], 400);
        }

        // Validate required fields - use Validator for API to avoid redirects
        $validator = \Validator::make($request->all(), [
            'homework_folder_id' => 'required|string',
            'file' => 'required|file|max:10240', // 10MB max
            'description' => 'nullable|string|max:1000',
            'homework_id' => 'nullable|integer|exists:academic_homework,homework_id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            $mobileApi = new MobileApiRepository();
            $authResult = $mobileApi->checkAuthCode($authCode);

            if (!$authResult['success']) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid authentication code'
                ], 401);
            }

            $teacherId = $authResult['user_id'];
            $userType = $authResult['user_type'];

            if ($userType !== 'staff') {
                return response()->json([
                    'success' => false,
                    'message' => 'Only staff members can upload homework files'
                ], 403);
            }

            // Get branch ID for permission checking
            $academicHelper = new \App\Library\Helper\AcademicHelper();
            $branchId = $academicHelper->mobileCurrentBranch($teacherId);

            // Check if user has homework Google Drive permissions
            if (!$this->checkHomeworkGoogleDriveAccess($teacherId, $branchId, 'write')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied. You do not have permission to upload homework files.'
                ], 403);
            }

            // Verify teacher owns the folder
            $homeworkFolder = \App\Models\HomeworkFolder::where('google_drive_folder_id', $request->input('homework_folder_id'))
                ->where('teacher_id', $teacherId)
                ->first();

            if (!$homeworkFolder) {
                return response()->json([
                    'success' => false,
                    'message' => 'Homework folder not found or access denied'
                ], 404);
            }

            // Check file upload restrictions
            \Log::info('Getting homework upload restrictions', [
                'teacher_id' => $teacherId,
                'branch_id' => $branchId,
                'homework_folder_id' => $request->input('homework_folder_id')
            ]);

            $restrictions = $this->getHomeworkUploadRestrictions($teacherId, $branchId);

            \Log::info('Homework upload restrictions retrieved', [
                'teacher_id' => $teacherId,
                'restrictions' => $restrictions,
                'restrictions_type' => gettype($restrictions)
            ]);

            $validation = $this->validateHomeworkFileUpload($request->file('file'), $restrictions);

            if (!$validation['valid']) {
                return response()->json([
                    'success' => false,
                    'message' => $validation['error']
                ], 400);
            }

            // Check if Google Drive is available
            if (!$this->driveRepository) {
                return response()->json([
                    'success' => false,
                    'message' => 'Google Drive service is not available'
                ], 503);
            }

            // Upload file with teacher ID
            $result = $this->driveRepository->uploadHomeworkFile(
                $request->file('file'),
                $request->input('homework_folder_id'),
                $request->input('description'),
                $teacherId  // Pass the teacher ID for uploaded_by field
            );

            if ($result['success']) {
                $responseData = [
                    'file_id' => $result['file']->id,
                    'google_drive_file_id' => $result['drive_file']['id'],
                    'file_name' => $result['file']->file_name,
                    'original_name' => $result['file']->original_name,
                    'file_size' => $result['file']->file_size,
                    'web_view_link' => $result['file']->web_view_link
                ];

                // If homework_id is provided, update the homework record with the uploaded file
                $homeworkId = $request->input('homework_id');
                if ($homeworkId) {
                    try {
                        $homework = \App\Models\Homework::find($homeworkId);
                        if ($homework && $homework->user_id == $teacherId) {
                            // Get existing Google Drive files or initialize empty array
                            $existingFiles = [];
                            if ($homework->google_drive_files) {
                                $existingFiles = json_decode($homework->google_drive_files, true) ?: [];
                            }

                            // Add the new file to the array
                            $newFile = [
                                'file_id' => $result['drive_file']['id'],
                                'file_name' => $result['file']->file_name,
                                'original_name' => $result['file']->original_name,
                                'web_view_link' => $result['file']->web_view_link,
                                'file_size' => $result['file']->file_size,
                                'uploaded_at' => now()->toISOString()
                            ];
                            $existingFiles[] = $newFile;

                            // Update homework record
                            $homework->google_drive_files = json_encode($existingFiles);
                            if (!$homework->google_drive_folder_id) {
                                $homework->google_drive_folder_id = $request->input('homework_folder_id');
                            }
                            $homework->save();

                            $responseData['homework_updated'] = true;
                            $responseData['homework_id'] = $homeworkId;
                            $responseData['total_files'] = count($existingFiles);

                            \Log::info('Homework record updated with uploaded file', [
                                'homework_id' => $homeworkId,
                                'file_id' => $result['drive_file']['id'],
                                'total_files' => count($existingFiles),
                                'teacher_id' => $teacherId
                            ]);
                        } else {
                            \Log::warning('Homework not found or access denied for file update', [
                                'homework_id' => $homeworkId,
                                'teacher_id' => $teacherId
                            ]);
                        }
                    } catch (\Exception $e) {
                        \Log::error('Error updating homework with uploaded file', [
                            'homework_id' => $homeworkId,
                            'error' => $e->getMessage()
                        ]);
                    }
                }

                return response()->json([
                    'success' => true,
                    'message' => 'File uploaded successfully',
                    'data' => $responseData
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to upload file',
                    'error' => $result['error']
                ], 500);
            }

        } catch (\Exception $e) {
            \Log::error('Upload homework file error', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload file'
            ], 500);
        }
    }

    /**
     * Upload student homework submission to Google Drive
     */
    public function uploadStudentHomeworkSubmission(Request $request)
    {
        $authCode = $request->input('auth_code');

        if (!$authCode) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code is required'
            ], 400);
        }

        // Validate required fields - use Validator for API to avoid redirects
        $validator = \Validator::make($request->all(), [
            'homework_id' => 'required|integer',
            'file' => 'required|file|max:10240', // 10MB max
            'submission_note' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            $mobileApi = new MobileApiRepository();
            $authResult = $mobileApi->checkAuthCode($authCode);

            if (!$authResult['success']) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid authentication code'
                ], 401);
            }

            $studentId = $authResult['user_id'];
            $userType = $authResult['user_type'];

            if ($userType !== 'student') {
                return response()->json([
                    'success' => false,
                    'message' => 'Only students can submit homework'
                ], 403);
            }

            // Get branch ID for permission checking
            $academicHelper = new \App\Library\Helper\AcademicHelper();
            $branchId = $academicHelper->mobileCurrentBranch($studentId);

            // Check if student has homework Google Drive permissions
            if (!$this->checkHomeworkGoogleDriveAccess($studentId, $branchId, 'write')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied. You do not have permission to upload homework submissions.'
                ], 403);
            }

            // Verify homework exists and student has access
            $homework = \App\Models\Homework::find($request->input('homework_id'));
            if (!$homework) {
                return response()->json([
                    'success' => false,
                    'message' => 'Homework not found'
                ], 404);
            }

            // Check if student is assigned to this homework
            $homeworkDetail = \App\Models\HomeworkDetail::where('homework_id', $homework->homework_id)
                ->where('student_id', $studentId)
                ->first();

            if (!$homeworkDetail) {
                return response()->json([
                    'success' => false,
                    'message' => 'You are not assigned to this homework'
                ], 403);
            }

            // Check file upload restrictions for students
            $restrictions = $this->getHomeworkUploadRestrictions($studentId, $branchId);
            $validation = $this->validateHomeworkFileUpload($request->file('file'), $restrictions);

            if (!$validation['valid']) {
                return response()->json([
                    'success' => false,
                    'message' => $validation['error']
                ], 400);
            }

            // Check if homework has Google Drive integration
            if (!$homework->google_drive_folder_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'This homework does not support Google Drive submissions'
                ], 400);
            }

            // Check if Google Drive is available
            if (!$this->driveRepository) {
                return response()->json([
                    'success' => false,
                    'message' => 'Google Drive service is not available'
                ], 503);
            }

            // Upload submission to Google Drive
            $result = $this->driveRepository->uploadStudentSubmission(
                $request->file('file'),
                $homework->homework_id,
                $studentId,
                $request->input('submission_note')
            );

            if ($result['success']) {
                // Update homework detail to mark as submitted
                $homeworkDetail->update([
                    'is_completed' => true,
                    'sumitted_date' => now(), // Note: keeping original field name for compatibility
                    'reply_data' => $request->input('submission_note')
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Homework submission uploaded successfully',
                    'data' => [
                        'submission_id' => $result['submission']->id,
                        'file_name' => $result['submission']->original_filename,
                        'file_size' => $result['submission']->formatted_size,
                        'web_view_link' => $result['submission']->web_view_link,
                        'submitted_at' => $result['submission']->submitted_at->format('Y-m-d H:i:s')
                    ]
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to upload submission',
                    'error' => $result['error']
                ], 500);
            }

        } catch (\Exception $e) {
            \Log::error('Upload student homework submission error', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload submission'
            ], 500);
        }
    }

    /**
     * Get student's homework submissions
     */
    public function getStudentHomeworkSubmissions(Request $request)
    {
        $authCode = $request->input('auth_code');
        $homeworkId = $request->input('homework_id');

        if (!$authCode) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code is required'
            ], 400);
        }

        try {
            $mobileApi = new MobileApiRepository();
            $authResult = $mobileApi->checkAuthCode($authCode);

            if (!$authResult['success']) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid authentication code'
                ], 401);
            }

            $studentId = $authResult['user_id'];
            $userType = $authResult['user_type'];

            if ($userType !== 'student') {
                return response()->json([
                    'success' => false,
                    'message' => 'Only students can access this endpoint'
                ], 403);
            }

            $query = \App\Models\HomeworkSubmission::where('student_id', $studentId);

            if ($homeworkId) {
                $query->where('homework_id', $homeworkId);
            }

            $submissions = $query->with(['homework', 'homework.teacher'])
                ->orderBy('submitted_at', 'desc')
                ->get();

            $submissionData = $submissions->map(function($submission) {
                return [
                    'id' => $submission->id,
                    'homework_id' => $submission->homework_id,
                    'homework_title' => $submission->homework->title ?? 'Unknown',
                    'teacher_name' => $submission->homework->teacher->name ?? 'Unknown',
                    'original_filename' => $submission->original_filename,
                    'file_type' => $submission->file_type,
                    'file_size' => $submission->formatted_size,
                    'web_view_link' => $submission->web_view_link,
                    'submission_note' => $submission->submission_note,
                    'submitted_at' => $submission->submitted_at->format('Y-m-d H:i:s'),
                    'is_image' => $submission->isImage(),
                    'is_document' => $submission->isDocument()
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $submissionData
            ]);

        } catch (\Exception $e) {
            \Log::error('Get student homework submissions error', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve submissions'
            ], 500);
        }
    }

    /**
     * Get student's accessible homework folders organized by subjects
     */
    public function getStudentHomeworkFolders(Request $request)
    {
        $authCode = $request->input('auth_code');

        if (!$authCode) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code is required'
            ], 400);
        }

        try {
            $mobileApi = new MobileApiRepository();
            $authResult = $mobileApi->checkAuthCode($authCode);

            if (!$authResult['success']) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid authentication code'
                ], 401);
            }

            $studentId = $authResult['user_id'];
            $userType = $authResult['user_type'];

            if ($userType !== 'student') {
                return response()->json([
                    'success' => false,
                    'message' => 'Only students can access this endpoint'
                ], 403);
            }

            // Get branch ID for permission checking
            $academicHelper = new \App\Library\Helper\AcademicHelper();
            $branchId = $academicHelper->mobileCurrentBranch($studentId);

            // Check if student has homework Google Drive permissions
            if (!$this->checkHomeworkGoogleDriveAccess($studentId, $branchId, 'read')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied. You do not have permission to access homework folders.'
                ], 403);
            }

            // Check if Google Drive is available
            if (!$this->driveRepository) {
                return response()->json([
                    'success' => false,
                    'message' => 'Google Drive service is not available'
                ], 503);
            }

            // Get student's accessible homework folders
            $folders = $this->driveRepository->getStudentHomeworkFolders($studentId);

            // Get student's subjects to organize folders
            $academicHelper = new \App\Library\Helper\AcademicHelper();
            $branchId = $academicHelper->mobileCurrentBranch($studentId);
            $academicYearId = $academicHelper->branchAcademicYear($branchId);

            // Get student's enrolled subjects
            $studentSubjects = \DB::table('academic_elective_grade_student')
                ->join('academic_elective_grade', 'academic_elective_grade_student.grade_id', '=', 'academic_elective_grade.grade_id')
                ->join('subjects', 'academic_elective_grade.subject_id', '=', 'subjects.subject_id')
                ->where('academic_elective_grade_student.student_id', $studentId)
                ->where('academic_elective_grade.academic_year_id', $academicYearId)
                ->where('academic_elective_grade.branch_id', $branchId)
                ->select(
                    'subjects.subject_id',
                    'subjects.subject_name',
                    'academic_elective_grade.grade_id',
                    'academic_elective_grade.grade_name'
                )
                ->get()
                ->groupBy('subject_name');

            // Organize folders by subjects
            $organizedData = [];

            foreach ($studentSubjects as $subjectName => $subjectGrades) {
                $subjectData = [
                    'subject_name' => $subjectName,
                    'subject_id' => $subjectGrades->first()->subject_id,
                    'grades' => [],
                    'folders' => []
                ];

                // Add grade information
                foreach ($subjectGrades as $grade) {
                    $subjectData['grades'][] = [
                        'grade_id' => $grade->grade_id,
                        'grade_name' => $grade->grade_name
                    ];

                    // Find folders for this grade
                    $gradeFolders = $folders->filter(function($folder) use ($grade) {
                        return in_array($grade->grade_id, $folder->assigned_classes ?? []);
                    });

                    foreach ($gradeFolders as $folder) {
                        // Get files in this folder
                        $folderFiles = $this->getHomeworkFolderFilesList($folder->google_drive_folder_id);

                        $subjectData['folders'][] = [
                            'id' => $folder->id,
                            'folder_name' => $folder->folder_name,
                            'description' => $folder->description,
                            'google_drive_folder_id' => $folder->google_drive_folder_id,
                            'teacher_name' => $folder->teacher->name ?? 'Unknown',
                            'assignment_type' => $folder->assignment_type,
                            'created_at' => $folder->created_at->format('Y-m-d H:i:s'),
                            'files' => $folderFiles,
                            'file_count' => count($folderFiles)
                        ];
                    }
                }

                // Remove duplicate folders
                $subjectData['folders'] = collect($subjectData['folders'])
                    ->unique('id')
                    ->values()
                    ->toArray();

                if (!empty($subjectData['folders'])) {
                    $organizedData[] = $subjectData;
                }
            }

            // Also include folders assigned directly to student (not subject-specific)
            $directFolders = $folders->filter(function($folder) use ($studentId) {
                return in_array($studentId, $folder->assigned_students ?? []);
            });

            if ($directFolders->isNotEmpty()) {
                $directFoldersData = [
                    'subject_name' => 'Personal Assignments',
                    'subject_id' => null,
                    'grades' => [],
                    'folders' => []
                ];

                foreach ($directFolders as $folder) {
                    $folderFiles = $this->getHomeworkFolderFilesList($folder->google_drive_folder_id);

                    $directFoldersData['folders'][] = [
                        'id' => $folder->id,
                        'folder_name' => $folder->folder_name,
                        'description' => $folder->description,
                        'google_drive_folder_id' => $folder->google_drive_folder_id,
                        'teacher_name' => $folder->teacher->name ?? 'Unknown',
                        'assignment_type' => $folder->assignment_type,
                        'created_at' => $folder->created_at->format('Y-m-d H:i:s'),
                        'files' => $folderFiles,
                        'file_count' => count($folderFiles)
                    ];
                }

                if (!empty($directFoldersData['folders'])) {
                    $organizedData[] = $directFoldersData;
                }
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'subjects' => $organizedData,
                    'total_subjects' => count($organizedData),
                    'total_folders' => $folders->count()
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Get student homework folders error', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve homework folders'
            ], 500);
        }
    }

    /**
     * Helper method to get files in a homework folder
     */
    private function getHomeworkFolderFilesList($googleDriveFolderId)
    {
        try {
            // Get files from Google Drive files table
            $files = \App\Models\GoogleDriveFile::where('folder_id', $googleDriveFolderId)
                ->where('is_active', true)
                ->orderBy('created_at', 'desc')
                ->get();

            return $files->map(function($file) {
                return [
                    'id' => $file->id,
                    'file_id' => $file->file_id,
                    'file_name' => $file->file_name,
                    'original_name' => $file->original_name,
                    'mime_type' => $file->mime_type,
                    'file_size' => $file->formatted_size,
                    'description' => $file->description,
                    'web_view_link' => $file->web_view_link,
                    'web_content_link' => $file->web_content_link,
                    'uploaded_by' => $file->uploader->name ?? 'Unknown',
                    'uploaded_at' => $file->created_at->format('Y-m-d H:i:s'),
                    'file_type' => $file->file_type ?? 'unknown'
                ];
            })->toArray();

        } catch (\Exception $e) {
            \Log::error('Error getting homework folder files: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Helper method to count students in assigned classes
     */
    private function getClassStudentCount($assignedClasses)
    {
        if (empty($assignedClasses)) {
            return 0;
        }

        try {
            return \DB::table('academic_elective_grade_students')
                ->whereIn('grade_id', $assignedClasses)
                ->distinct('student_id')
                ->count('student_id');
        } catch (\Exception $e) {
            \Log::error('Error counting class students: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get files in a specific homework folder for student
     */
    public function getHomeworkFolderFiles(Request $request)
    {
        $authCode = $request->input('auth_code');
        $folderId = $request->input('folder_id');

        if (!$authCode || !$folderId) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code and folder ID are required'
            ], 400);
        }

        try {
            $mobileApi = new MobileApiRepository();
            $authResult = $mobileApi->checkAuthCode($authCode);

            if (!$authResult['success']) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid authentication code'
                ], 401);
            }

            $studentId = $authResult['user_id'];
            $userType = $authResult['user_type'];

            if ($userType !== 'student') {
                return response()->json([
                    'success' => false,
                    'message' => 'Only students can access this endpoint'
                ], 403);
            }

            // Get branch ID for permission checking
            $academicHelper = new \App\Library\Helper\AcademicHelper();
            $branchId = $academicHelper->mobileCurrentBranch($studentId);

            // Check if student has homework Google Drive permissions
            if (!$this->checkHomeworkGoogleDriveAccess($studentId, $branchId, 'read')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied. You do not have permission to access homework folder files.'
                ], 403);
            }

            // Verify student has access to this folder
            $homeworkFolder = \App\Models\HomeworkFolder::find($folderId);
            if (!$homeworkFolder) {
                return response()->json([
                    'success' => false,
                    'message' => 'Homework folder not found'
                ], 404);
            }

            // Check if student has access to this folder
            $hasAccess = false;

            // Check class assignment
            if ($homeworkFolder->assignment_type === 'class' || $homeworkFolder->assignment_type === 'mixed') {
                $studentClasses = \DB::table('academic_elective_grade_students')
                    ->where('student_id', $studentId)
                    ->pluck('grade_id')
                    ->toArray();

                $assignedClasses = $homeworkFolder->assigned_classes ?? [];
                $hasAccess = !empty(array_intersect($studentClasses, $assignedClasses));
            }

            // Check direct assignment
            if (!$hasAccess && ($homeworkFolder->assignment_type === 'student' || $homeworkFolder->assignment_type === 'mixed')) {
                $assignedStudents = $homeworkFolder->assigned_students ?? [];
                $hasAccess = in_array($studentId, $assignedStudents);
            }

            if (!$hasAccess) {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied to this homework folder'
                ], 403);
            }

            // Get files in the folder
            $files = $this->getHomeworkFolderFilesList($homeworkFolder->google_drive_folder_id);

            return response()->json([
                'success' => true,
                'data' => [
                    'folder' => [
                        'id' => $homeworkFolder->id,
                        'folder_name' => $homeworkFolder->folder_name,
                        'description' => $homeworkFolder->description,
                        'teacher_name' => $homeworkFolder->teacher->name ?? 'Unknown',
                        'created_at' => $homeworkFolder->created_at->format('Y-m-d H:i:s')
                    ],
                    'files' => $files,
                    'file_count' => count($files)
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Get homework folder files error', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve folder files'
            ], 500);
        }
    }

    /**
     * Get teacher's homework folders as a simple flat list
     */
    public function getTeacherHomeworkFoldersSimple(Request $request)
    {
        $authCode = $request->input('auth_code');

        if (!$authCode) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication code is required'
            ], 400);
        }

        try {
            $mobileApi = new MobileApiRepository();
            $authResult = $mobileApi->checkAuthCode($authCode);

            if (!$authResult['success']) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid authentication code'
                ], 401);
            }

            $teacherId = $authResult['user_id'];
            $userType = $authResult['user_type'];

            if ($userType !== 'staff') {
                return response()->json([
                    'success' => false,
                    'message' => 'Only staff members can access homework folders'
                ], 403);
            }

            // Get branch ID for permission checking
            $academicHelper = new \App\Library\Helper\AcademicHelper();
            $branchId = $academicHelper->mobileCurrentBranch($teacherId);

            if (!$branchId) {
                Log::error('Branch ID not found for teacher in simple method', [
                    'teacher_id' => $teacherId,
                    'auth_code' => $authCode
                ]);
                return response()->json([
                    'success' => false,
                    'error' => 'Branch information not found for teacher'
                ], 404);
            }

            // Check if teacher has homework Google Drive permissions
            if (!$this->checkHomeworkGoogleDriveAccess($teacherId, $branchId, 'read')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied. You do not have permission to access homework folders.'
                ], 403);
            }

            // Check if Google Drive is available
            if (!$this->driveRepository) {
                return response()->json([
                    'success' => false,
                    'message' => 'Google Drive service is not available'
                ], 503);
            }

            // Get academic year for proper folder filtering
            $academicYearId = $academicHelper->branchAcademicYear($branchId);
            if (!$academicYearId || !is_numeric($academicYearId)) {
                Log::error('Academic year not found for branch in simple method', [
                    'teacher_id' => $teacherId,
                    'branch_id' => $branchId,
                    'auth_code' => $authCode,
                    'academic_year_result' => $academicYearId
                ]);
                return response()->json([
                    'success' => false,
                    'error' => 'Academic year not found for branch'
                ], 404);
            }

            // Get teacher's homework folders with proper parameters
            $folders = $this->driveRepository->getTeacherHomeworkFolders($teacherId, $branchId, $academicYearId);

            $folderData = $folders->map(function($folder) {
                $folderFiles = $this->getHomeworkFolderFilesList($folder->google_drive_folder_id);

                return [
                    'id' => $folder->id,
                    'folder_name' => $folder->folder_name,
                    'description' => $folder->description,
                    'google_drive_folder_id' => $folder->google_drive_folder_id,
                    'assignment_type' => $folder->assignment_type,
                    'assigned_classes' => $folder->assignedGrades->map(function($grade) {
                        return [
                            'grade_id' => $grade->grade_id,
                            'grade_name' => $grade->grade_name
                        ];
                    })->toArray(),
                    'assigned_students' => $folder->assignedStudents->map(function($student) {
                        return [
                            'student_id' => $student->id,
                            'student_name' => $student->name
                        ];
                    })->toArray(),
                    'created_at' => $folder->created_at->format('Y-m-d H:i:s'),
                    'is_active' => $folder->is_active,
                    'file_count' => count($folderFiles),
                    'student_count' => count($folder->assignedStudents) + $this->getClassStudentCount($folder->assigned_classes ?? [])
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $folderData,
                'total_folders' => $folderData->count()
            ]);

        } catch (\Exception $e) {
            \Log::error('Get teacher homework folders simple error', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve homework folders'
            ], 500);
        }
    }
}
